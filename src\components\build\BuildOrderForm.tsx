import React, { useState, useEffect } from 'react';
import { X, Save, Package, Calendar, User, MapPin, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { BuildOrder, Part } from '../../types';

interface BuildOrderFormProps {
  buildOrder?: BuildOrder;
  isOpen: boolean;
  onClose: () => void;
  onSave: (buildData: BuildOrderFormData) => Promise<void>;
}

interface BuildOrderFormData {
  reference: string;
  title: string;
  part: number;
  quantity: number;
  priority: number;
  target_date: string;
  take_from: number;
  destination: number;
  parent: number | null;
  sales_order: number | null;
  responsible: number;
  notes: string;
  link: string;
}

const BuildOrderForm: React.FC<BuildOrderFormProps> = ({ buildOrder, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState<BuildOrderFormData>({
    reference: '',
    title: '',
    part: 0,
    quantity: 1,
    priority: 50,
    target_date: '',
    take_from: 1,
    destination: 1,
    parent: null,
    sales_order: null,
    responsible: 1,
    notes: '',
    link: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mock data
  const assemblies: Part[] = [
    { pk: 1, name: 'Arduino Development Kit', IPN: 'KIT-ARD-DEV-001', assembly: true, component: false, active: true, in_stock: 5, minimum_stock: 2, units: 'pcs', description: 'Complete Arduino development kit', category: 1, category_name: 'Kits', IPN: 'KIT-ARD-DEV-001', keywords: '', link: '', revision: '1.0', image: '', default_location: 1, default_supplier: 1, virtual: false, salable: true, purchaseable: false, trackable: true, stock_item_count: 0, building: 0, can_build: 10 },
    { pk: 2, name: 'LED Display Module', IPN: 'MOD-LED-DISP-001', assembly: true, component: false, active: true, in_stock: 12, minimum_stock: 5, units: 'pcs', description: 'LED display module assembly', category: 2, category_name: 'Modules', IPN: 'MOD-LED-DISP-001', keywords: '', link: '', revision: '1.0', image: '', default_location: 1, default_supplier: 1, virtual: false, salable: true, purchaseable: false, trackable: true, stock_item_count: 0, building: 0, can_build: 25 },
  ];

  const locations = [
    { id: 1, name: 'Main Warehouse - A1' },
    { id: 2, name: 'Production Floor' },
    { id: 3, name: 'Assembly Area' },
    { id: 4, name: 'Quality Control' },
  ];

  const users = [
    { id: 1, name: 'John Doe', role: 'Production Manager' },
    { id: 2, name: 'Jane Smith', role: 'Assembly Technician' },
    { id: 3, name: 'Mike Johnson', role: 'Quality Inspector' },
  ];

  const priorities = [
    { value: 10, label: 'Low', color: 'text-gray-600' },
    { value: 50, label: 'Normal', color: 'text-blue-600' },
    { value: 100, label: 'High', color: 'text-orange-600' },
    { value: 200, label: 'Urgent', color: 'text-red-600' },
  ];

  useEffect(() => {
    if (buildOrder) {
      setFormData({
        reference: buildOrder.reference,
        title: buildOrder.title,
        part: buildOrder.part,
        quantity: buildOrder.quantity,
        priority: buildOrder.priority,
        target_date: buildOrder.target_date.split('T')[0],
        take_from: buildOrder.take_from,
        destination: buildOrder.destination,
        parent: buildOrder.parent,
        sales_order: buildOrder.sales_order,
        responsible: buildOrder.responsible,
        notes: buildOrder.notes,
        link: buildOrder.link,
      });
    } else {
      // Reset form for new build order
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      setFormData({
        reference: generateReference(),
        title: '',
        part: 0,
        quantity: 1,
        priority: 50,
        target_date: tomorrow.toISOString().split('T')[0],
        take_from: 1,
        destination: 1,
        parent: null,
        sales_order: null,
        responsible: 1,
        notes: '',
        link: '',
      });
    }
    setErrors({});
  }, [buildOrder, isOpen]);

  const generateReference = (): string => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `BO-${date}-${random}`;
  };

  const handleInputChange = (field: keyof BuildOrderFormData, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-generate title if part changes
      if (field === 'part' && value > 0) {
        const selectedPart = assemblies.find(p => p.pk === value);
        if (selectedPart && !prev.title) {
          updated.title = `Build ${selectedPart.name}`;
        }
      }
      
      return updated;
    });
    
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.reference.trim()) {
      newErrors.reference = 'Reference is required';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (formData.part === 0) {
      newErrors.part = 'Assembly part is required';
    }

    if (formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    if (!formData.target_date) {
      newErrors.target_date = 'Target date is required';
    }

    if (formData.responsible === 0) {
      newErrors.responsible = 'Responsible person is required';
    }

    // Check if selected part can be built
    const selectedPart = assemblies.find(p => p.pk === formData.part);
    if (selectedPart && formData.quantity > selectedPart.can_build) {
      newErrors.quantity = `Cannot build more than ${selectedPart.can_build} units (insufficient stock)`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Failed to save build order:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const selectedPart = assemblies.find(p => p.pk === formData.part);
  const selectedPriority = priorities.find(p => p.value === formData.priority);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-50 rounded-lg">
                <Package className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">
                  {buildOrder ? 'Edit Build Order' : 'Create Build Order'}
                </h2>
                <p className="text-sm text-gray-500">
                  {buildOrder ? 'Update build order information' : 'Create a new manufacturing build order'}
                </p>
              </div>
            </div>
            <Button type="button" variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Build Order Information</CardTitle>
                <CardDescription>Basic details about the build order</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Reference *
                    </label>
                    <div className="flex space-x-2">
                      <Input
                        value={formData.reference}
                        onChange={(e) => handleInputChange('reference', e.target.value)}
                        placeholder="Build order reference"
                        className={errors.reference ? 'border-red-500' : ''}
                      />
                      <Button type="button" variant="outline" onClick={() => handleInputChange('reference', generateReference())}>
                        Generate
                      </Button>
                    </div>
                    {errors.reference && <p className="text-red-500 text-xs mt-1">{errors.reference}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Assembly Part *
                    </label>
                    <select
                      value={formData.part}
                      onChange={(e) => handleInputChange('part', parseInt(e.target.value))}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                        errors.part ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value={0}>Select assembly to build</option>
                      {assemblies.map((part) => (
                        <option key={part.pk} value={part.pk}>
                          {part.name} ({part.IPN}) - Can build: {part.can_build}
                        </option>
                      ))}
                    </select>
                    {errors.part && <p className="text-red-500 text-xs mt-1">{errors.part}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Title *
                  </label>
                  <Input
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Build order title"
                    className={errors.title ? 'border-red-500' : ''}
                  />
                  {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Quantity *
                    </label>
                    <Input
                      type="number"
                      value={formData.quantity}
                      onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                      min="1"
                      className={errors.quantity ? 'border-red-500' : ''}
                    />
                    {errors.quantity && <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Priority
                    </label>
                    <select
                      value={formData.priority}
                      onChange={(e) => handleInputChange('priority', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      {priorities.map((priority) => (
                        <option key={priority.value} value={priority.value}>
                          {priority.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Target Date *
                    </label>
                    <Input
                      type="date"
                      value={formData.target_date}
                      onChange={(e) => handleInputChange('target_date', e.target.value)}
                      className={errors.target_date ? 'border-red-500' : ''}
                    />
                    {errors.target_date && <p className="text-red-500 text-xs mt-1">{errors.target_date}</p>}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Locations and Assignment */}
            <Card>
              <CardHeader>
                <CardTitle>Locations and Assignment</CardTitle>
                <CardDescription>Specify where to source materials and assign responsibility</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Take From Location
                    </label>
                    <select
                      value={formData.take_from}
                      onChange={(e) => handleInputChange('take_from', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      {locations.map((location) => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Destination Location
                    </label>
                    <select
                      value={formData.destination}
                      onChange={(e) => handleInputChange('destination', parseInt(e.target.value))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      {locations.map((location) => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Responsible Person *
                    </label>
                    <select
                      value={formData.responsible}
                      onChange={(e) => handleInputChange('responsible', parseInt(e.target.value))}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                        errors.responsible ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value={0}>Select responsible person</option>
                      {users.map((user) => (
                        <option key={user.id} value={user.id}>
                          {user.name} ({user.role})
                        </option>
                      ))}
                    </select>
                    {errors.responsible && <p className="text-red-500 text-xs mt-1">{errors.responsible}</p>}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Information */}
            <Card>
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
                <CardDescription>Optional details and references</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => handleInputChange('notes', e.target.value)}
                    placeholder="Additional notes about this build order"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    External Link
                  </label>
                  <Input
                    value={formData.link}
                    onChange={(e) => handleInputChange('link', e.target.value)}
                    placeholder="https://..."
                  />
                </div>
              </CardContent>
            </Card>

            {/* Build Summary */}
            {selectedPart && (
              <Card>
                <CardHeader>
                  <CardTitle>Build Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="flex items-center space-x-3">
                      <Package className="w-8 h-8 text-blue-600" />
                      <div>
                        <p className="text-sm text-gray-600">Assembly</p>
                        <p className="font-medium">{selectedPart.name}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-8 h-8 text-green-600" />
                      <div>
                        <p className="text-sm text-gray-600">Target Date</p>
                        <p className="font-medium">{formData.target_date}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        selectedPriority?.value === 200 ? 'bg-red-100' :
                        selectedPriority?.value === 100 ? 'bg-orange-100' :
                        selectedPriority?.value === 50 ? 'bg-blue-100' : 'bg-gray-100'
                      }`}>
                        <span className={`text-sm font-medium ${selectedPriority?.color}`}>
                          {selectedPriority?.label.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Priority</p>
                        <p className={`font-medium ${selectedPriority?.color}`}>
                          {selectedPriority?.label}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <User className="w-8 h-8 text-purple-600" />
                      <div>
                        <p className="text-sm text-gray-600">Responsible</p>
                        <p className="font-medium">
                          {users.find(u => u.id === formData.responsible)?.name || 'Not assigned'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {formData.quantity > selectedPart.can_build && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-2">
                      <AlertTriangle className="w-5 h-5 text-yellow-600" />
                      <span className="text-sm text-yellow-800">
                        Warning: Requested quantity ({formData.quantity}) exceeds available build capacity ({selectedPart.can_build})
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {buildOrder ? 'Update Build Order' : 'Create Build Order'}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BuildOrderForm;
