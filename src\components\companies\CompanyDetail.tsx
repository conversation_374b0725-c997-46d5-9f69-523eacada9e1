import React, { useState } from 'react';
import { X, Edit, Trash2, Building, Globe, Phone, Mail, MapPin, DollarSign, Package, ShoppingCart, TrendingUp, Users } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { Company } from '../../types';

interface CompanyDetailProps {
  company: Company;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (company: Company) => void;
  onDelete: (company: Company) => void;
}

const CompanyDetail: React.FC<CompanyDetailProps> = ({
  company,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'orders' | 'parts' | 'contacts'>('overview');

  if (!isOpen) return null;

  const getCompanyTypes = () => {
    const types = [];
    if (company.is_customer) types.push('Customer');
    if (company.is_supplier) types.push('Supplier');
    if (company.is_manufacturer) types.push('Manufacturer');
    return types;
  };

  const mockOrders = [
    { id: 1, reference: 'PO-2024-001', type: 'Purchase', status: 'Complete', date: '2024-01-15', total: '$1,250.00' },
    { id: 2, reference: 'SO-2024-005', type: 'Sales', status: 'Shipped', date: '2024-01-18', total: '$850.00' },
    { id: 3, reference: 'PO-2024-003', type: 'Purchase', status: 'Pending', date: '2024-01-20', total: '$2,100.00' },
  ];

  const mockParts = [
    { id: 1, name: 'Arduino Uno R3', ipn: 'ARD-UNO-R3', price: '$24.95', stock: 25 },
    { id: 2, name: 'Resistor 10kΩ', ipn: 'RES-10K-0.25W', price: '$0.05', stock: 450 },
    { id: 3, name: 'LED Red 5mm', ipn: 'LED-RED-5MM', price: '$0.15', stock: 89 },
  ];

  const mockContacts = [
    { id: 1, name: 'John Smith', role: 'Sales Manager', email: '<EMAIL>', phone: '+****************' },
    { id: 2, name: 'Sarah Johnson', role: 'Technical Support', email: '<EMAIL>', phone: '+****************' },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-purple-50 rounded-lg">
              <Building className="w-8 h-8 text-purple-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{company.name}</h2>
              <p className="text-gray-600">{company.description}</p>
              <div className="flex items-center space-x-2 mt-1">
                {getCompanyTypes().map((type, index) => (
                  <span key={index} className="px-2 py-1 text-xs rounded-full bg-blue-50 text-blue-600">
                    {type}
                  </span>
                ))}
                {company.active ? (
                  <span className="px-2 py-1 text-xs rounded-full text-green-600 bg-green-50">Active</span>
                ) : (
                  <span className="px-2 py-1 text-xs rounded-full text-gray-600 bg-gray-50">Inactive</span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onEdit(company)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={() => onDelete(company)}>
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'orders', label: 'Orders' },
              { id: 'parts', label: 'Parts' },
              { id: 'contacts', label: 'Contacts' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Orders</p>
                        <p className="text-2xl font-bold text-gray-900">12</p>
                      </div>
                      <ShoppingCart className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Value</p>
                        <p className="text-2xl font-bold text-green-600">$15,420</p>
                      </div>
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Parts</p>
                        <p className="text-2xl font-bold text-purple-600">45</p>
                      </div>
                      <Package className="w-8 h-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Contacts</p>
                        <p className="text-2xl font-bold text-orange-600">3</p>
                      </div>
                      <Users className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Company Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {company.email && (
                      <div className="flex items-center space-x-3">
                        <Mail className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Email</p>
                          <a href={`mailto:${company.email}`} className="text-sm text-blue-600 hover:underline">
                            {company.email}
                          </a>
                        </div>
                      </div>
                    )}
                    
                    {company.phone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Phone</p>
                          <a href={`tel:${company.phone}`} className="text-sm text-blue-600 hover:underline">
                            {company.phone}
                          </a>
                        </div>
                      </div>
                    )}
                    
                    {company.website && (
                      <div className="flex items-center space-x-3">
                        <Globe className="w-4 h-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Website</p>
                          <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                            {company.website}
                          </a>
                        </div>
                      </div>
                    )}
                    
                    {company.address && (
                      <div className="flex items-start space-x-3">
                        <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                        <div>
                          <p className="text-sm font-medium text-gray-700">Address</p>
                          <p className="text-sm text-gray-600 whitespace-pre-line">{company.address}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Company Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Currency:</span>
                      <span className="font-medium">{company.currency}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Primary Contact:</span>
                      <span className="font-medium">{company.contact || 'Not specified'}</span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Company Types:</span>
                      <div className="flex flex-wrap gap-1">
                        {getCompanyTypes().map((type, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                            {type}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className={`font-medium ${company.active ? 'text-green-600' : 'text-red-600'}`}>
                        {company.active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    
                    {company.link && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">External Link:</span>
                        <a href={company.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                          View
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'orders' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                  <CardDescription>Order history with this company</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Reference</th>
                          <th className="text-left py-3 px-4">Type</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-left py-3 px-4">Date</th>
                          <th className="text-right py-3 px-4">Total</th>
                        </tr>
                      </thead>
                      <tbody>
                        {mockOrders.map((order) => (
                          <tr key={order.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4 font-medium">{order.reference}</td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                order.type === 'Purchase' ? 'bg-blue-50 text-blue-600' : 'bg-green-50 text-green-600'
                              }`}>
                                {order.type}
                              </span>
                            </td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                order.status === 'Complete' ? 'bg-green-50 text-green-600' :
                                order.status === 'Shipped' ? 'bg-blue-50 text-blue-600' :
                                'bg-yellow-50 text-yellow-600'
                              }`}>
                                {order.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-gray-600">{order.date}</td>
                            <td className="py-3 px-4 text-right font-medium">{order.total}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'parts' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Parts Catalog</CardTitle>
                  <CardDescription>Parts available from this company</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {mockParts.map((part) => (
                      <div key={part.id} className="p-4 border rounded-lg">
                        <h4 className="font-medium text-gray-900">{part.name}</h4>
                        <p className="text-sm text-gray-600 mb-2">{part.ipn}</p>
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-bold text-green-600">{part.price}</span>
                          <span className="text-sm text-gray-500">Stock: {part.stock}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'contacts' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Company Contacts</CardTitle>
                  <CardDescription>Key contacts at this company</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockContacts.map((contact) => (
                      <div key={contact.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="p-2 bg-gray-100 rounded-full">
                            <Users className="w-5 h-5 text-gray-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{contact.name}</h4>
                            <p className="text-sm text-gray-600">{contact.role}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-600">{contact.email}</p>
                          <p className="text-sm text-gray-600">{contact.phone}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CompanyDetail;
