import React, { useState, useEffect } from 'react';
import { Plus, Search, Eye, Edit, Wrench, Calendar, Package, CheckCircle, Clock, Download, Trash2, Settings, Play, Pause, Square } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent } from '../../components/ui/Card';
import BuildOrderForm from '../../components/build/BuildOrderForm';
import BOMForm from '../../components/build/BOMForm';
import AdvancedFilter, { buildOrderFilters } from '../../components/filters/AdvancedFilter';
import type { BuildOrder, Part } from '../../types';
import { format } from 'date-fns';

const BuildPage: React.FC = () => {
  const [buildOrders, setBuildOrders] = useState<BuildOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrders, setSelectedOrders] = useState<number[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // Modal states
  const [isBuildFormOpen, setIsBuildFormOpen] = useState(false);
  const [isBOMFormOpen, setIsBOMFormOpen] = useState(false);
  const [selectedBuildOrder, setSelectedBuildOrder] = useState<BuildOrder | null>(null);
  const [editingBuildOrder, setEditingBuildOrder] = useState<BuildOrder | null>(null);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);

  useEffect(() => {
    fetchBuildOrders();
  }, [searchTerm, filterValues]);

  const fetchBuildOrders = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for demonstration
      const mockBuildOrders: BuildOrder[] = [
        {
          pk: 1,
          reference: 'BO-2024-001',
          title: 'Arduino Sensor Module Assembly',
          part: 1,
          part_detail: {
            pk: 1,
            name: 'Arduino Sensor Module',
            description: 'Complete sensor module with Arduino Uno and sensors',
            category: 1,
            IPN: 'ASM-SENSOR-001',
            revision: '1.0',
            keywords: 'arduino, sensor, module',
            link: '',
            image: '',
            default_location: 1,
            default_supplier: 1,
            minimum_stock: 5,
            units: 'pcs',
            salable: true,
            assembly: true,
            component: false,
            purchaseable: false,
            trackable: true,
            active: true,
            virtual: false,
            in_stock: 3,
            stock_item_count: 1,
            building: 10,
            can_build: 5,
          },
          quantity: 10,
          completed: 3,
          status: 20,
          status_text: 'Production',
          creation_date: '2024-01-10T09:00:00Z',
          target_date: '2024-01-25T17:00:00Z',
          completion_date: '',
          notes: 'Priority build for customer order SO-2024-001',
          link: '',
        },
        {
          pk: 2,
          reference: 'BO-2024-002',
          title: 'LED Display Panel Build',
          part: 2,
          part_detail: {
            pk: 2,
            name: 'LED Display Panel 8x8',
            description: '8x8 LED matrix display panel with driver circuit',
            category: 2,
            IPN: 'LED-PANEL-8X8',
            revision: '2.1',
            keywords: 'led, display, matrix',
            link: '',
            image: '',
            default_location: 2,
            default_supplier: 2,
            minimum_stock: 10,
            units: 'pcs',
            salable: true,
            assembly: true,
            component: false,
            purchaseable: false,
            trackable: true,
            active: true,
            virtual: false,
            in_stock: 15,
            stock_item_count: 2,
            building: 25,
            can_build: 12,
          },
          quantity: 25,
          completed: 0,
          status: 10,
          status_text: 'Pending',
          creation_date: '2024-01-12T14:30:00Z',
          target_date: '2024-02-01T17:00:00Z',
          completion_date: '',
          notes: 'Waiting for LED components to arrive',
          link: '',
        },
        {
          pk: 3,
          reference: 'BO-2024-003',
          title: 'Power Supply Unit Assembly',
          part: 3,
          part_detail: {
            pk: 3,
            name: 'Switching Power Supply 12V 5A',
            description: '12V 5A switching power supply with protection circuits',
            category: 3,
            IPN: 'PSU-12V-5A',
            revision: '1.2',
            keywords: 'power, supply, 12v, switching',
            link: '',
            image: '',
            default_location: 3,
            default_supplier: 1,
            minimum_stock: 8,
            units: 'pcs',
            salable: true,
            assembly: true,
            component: false,
            purchaseable: false,
            trackable: true,
            active: true,
            virtual: false,
            in_stock: 12,
            stock_item_count: 3,
            building: 0,
            can_build: 15,
          },
          quantity: 15,
          completed: 15,
          status: 40,
          status_text: 'Complete',
          creation_date: '2024-01-05T10:15:00Z',
          target_date: '2024-01-20T17:00:00Z',
          completion_date: '2024-01-18T16:30:00Z',
          notes: 'Completed ahead of schedule, all units tested and passed QC',
          link: '',
        },
      ];

      // Filter by search term
      const filteredOrders = mockBuildOrders.filter(order =>
        order.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.part_detail.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.part_detail.IPN.toLowerCase().includes(searchTerm.toLowerCase())
      );

      setBuildOrders(filteredOrders);
    } catch (error) {
      console.error('Failed to fetch build orders:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // CRUD Operations
  const handleCreateBuildOrder = () => {
    setEditingBuildOrder(null);
    setIsBuildFormOpen(true);
  };

  const handleViewBuildOrder = (order: BuildOrder) => {
    setSelectedBuildOrder(order);
    // TODO: Open build order detail modal
    console.log('View build order:', order);
  };

  const handleEditBuildOrder = (order: BuildOrder) => {
    setEditingBuildOrder(order);
    setIsBuildFormOpen(true);
  };

  const handleDeleteBuildOrder = async (order: BuildOrder) => {
    if (window.confirm(`Are you sure you want to delete build order "${order.reference}"?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setBuildOrders(prev => prev.filter(bo => bo.pk !== order.pk));
      } catch (error) {
        console.error('Failed to delete build order:', error);
        alert('Failed to delete build order. Please try again.');
      }
    }
  };

  const handleSaveBuildOrder = async (buildData: any) => {
    try {
      if (editingBuildOrder) {
        // Update existing build order
        const updatedOrder = {
          ...editingBuildOrder,
          ...buildData,
        };
        setBuildOrders(prev => prev.map(bo => bo.pk === editingBuildOrder.pk ? updatedOrder : bo));
      } else {
        // Create new build order
        const newOrder: BuildOrder = {
          pk: Date.now(),
          ...buildData,
          part_detail: mockParts.find(p => p.pk === buildData.part)!,
          completed: 0,
          status: 10,
          status_text: 'Pending',
          creation_date: new Date().toISOString(),
          completion_date: '',
          take_from: buildData.take_from,
          destination: buildData.destination,
          parent: buildData.parent,
          sales_order: buildData.sales_order,
          responsible: buildData.responsible,
          priority: buildData.priority,
        };
        setBuildOrders(prev => [newOrder, ...prev]);
      }

      setIsBuildFormOpen(false);
      setEditingBuildOrder(null);
    } catch (error) {
      console.error('Failed to save build order:', error);
      throw error;
    }
  };

  // BOM Operations
  const handleManageBOM = (part: Part) => {
    setSelectedPart(part);
    setIsBOMFormOpen(true);
  };

  const handleSaveBOM = async (bomData: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('BOM saved:', bomData);
      setIsBOMFormOpen(false);
      setSelectedPart(null);
    } catch (error) {
      console.error('Failed to save BOM:', error);
      throw error;
    }
  };

  // Build Order Status Operations
  const handleStartBuild = async (order: BuildOrder) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      setBuildOrders(prev => prev.map(bo =>
        bo.pk === order.pk
          ? { ...bo, status: 20, status_text: 'Production' }
          : bo
      ));
    } catch (error) {
      console.error('Failed to start build:', error);
    }
  };

  const handlePauseBuild = async (order: BuildOrder) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      setBuildOrders(prev => prev.map(bo =>
        bo.pk === order.pk
          ? { ...bo, status: 25, status_text: 'On Hold' }
          : bo
      ));
    } catch (error) {
      console.error('Failed to pause build:', error);
    }
  };

  const handleCompleteBuild = async (order: BuildOrder) => {
    if (window.confirm(`Mark build order "${order.reference}" as complete?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setBuildOrders(prev => prev.map(bo =>
          bo.pk === order.pk
            ? {
                ...bo,
                status: 40,
                status_text: 'Complete',
                completed: bo.quantity,
                completion_date: new Date().toISOString()
              }
            : bo
        ));
      } catch (error) {
        console.error('Failed to complete build:', error);
      }
    }
  };

  // Mock parts data for build orders
  const mockParts: Part[] = [
    { pk: 1, name: 'Arduino Development Kit', IPN: 'KIT-ARD-DEV-001', assembly: true, component: false, active: true, in_stock: 5, minimum_stock: 2, units: 'pcs', description: 'Complete Arduino development kit', category: 1, category_name: 'Kits', keywords: '', link: '', revision: '1.0', image: '', default_location: 1, default_supplier: 1, virtual: false, salable: true, purchaseable: false, trackable: true, stock_item_count: 0, building: 0, can_build: 10 },
    { pk: 2, name: 'LED Display Module', IPN: 'MOD-LED-DISP-001', assembly: true, component: false, active: true, in_stock: 12, minimum_stock: 5, units: 'pcs', description: 'LED display module assembly', category: 2, category_name: 'Modules', keywords: '', link: '', revision: '1.0', image: '', default_location: 1, default_supplier: 1, virtual: false, salable: true, purchaseable: false, trackable: true, stock_item_count: 0, building: 0, can_build: 25 },
    { pk: 3, name: 'Power Supply Unit', IPN: 'PSU-12V-5A', assembly: true, component: false, active: true, in_stock: 8, minimum_stock: 3, units: 'pcs', description: 'Switching power supply unit', category: 3, category_name: 'Power', keywords: '', link: '', revision: '1.2', image: '', default_location: 1, default_supplier: 1, virtual: false, salable: true, purchaseable: false, trackable: true, stock_item_count: 0, building: 0, can_build: 15 },
  ];

  // Filter operations
  const handleFilterChange = (values: Record<string, any>) => {
    setFilterValues(values);
  };

  const handleFilterApply = () => {
    fetchBuildOrders();
  };

  const handleFilterClear = () => {
    setFilterValues({});
    fetchBuildOrders();
  };

  // Bulk operations
  const handleSelectOrder = (orderId: number) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = () => {
    if (selectedOrders.length === buildOrders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(buildOrders.map(bo => bo.pk));
    }
  };

  const handleExport = () => {
    const csvContent = [
      'Reference,Title,Part,Quantity,Completed,Status,Created,Target Date,Notes',
      ...buildOrders.map(order =>
        `"${order.reference}","${order.title}","${order.part_detail.name}",${order.quantity},${order.completed},"${order.status_text}","${order.creation_date}","${order.target_date}","${order.notes}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'build_orders_export.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'production':
        return 'text-blue-600 bg-blue-50';
      case 'complete':
        return 'text-green-600 bg-green-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getProgressPercentage = (completed: number, total: number) => {
    return total > 0 ? Math.round((completed / total) * 100) : 0;
  };

  const getProgressColor = (percentage: number) => {
    if (percentage === 100) return 'bg-green-500';
    if (percentage >= 50) return 'bg-blue-500';
    if (percentage > 0) return 'bg-yellow-500';
    return 'bg-gray-300';
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Build Orders</h1>
          <p className="text-gray-600 mt-2">Manage assembly and manufacturing orders</p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedOrders.length > 0 && (
            <>
              <Button variant="outline" onClick={() => setSelectedOrders([])}>
                Clear ({selectedOrders.length})
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export Selected
              </Button>
            </>
          )}
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
          <Button onClick={handleCreateBuildOrder}>
            <Plus className="w-4 h-4 mr-2" />
            Create Build Order
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search build orders by reference, title, or part..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <AdvancedFilter
              title="Build Order Filters"
              description="Filter build orders by various criteria"
              filters={buildOrderFilters}
              values={filterValues}
              onChange={handleFilterChange}
              onApply={handleFilterApply}
              onClear={handleFilterClear}
            />
          </div>

          {selectedOrders.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedOrders.length} build order{selectedOrders.length !== 1 ? 's' : ''} selected
              </span>
              <Button variant="ghost" size="sm" onClick={() => setSelectedOrders([])}>
                Clear selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Build Orders List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Bulk Actions Header */}
          {buildOrders.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedOrders.length === buildOrders.length && buildOrders.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">
                  {buildOrders.length} build order{buildOrders.length !== 1 ? 's' : ''} total
                </span>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {buildOrders.map((order) => {
              const progressPercentage = getProgressPercentage(order.completed, order.quantity);
            
              return (
                <Card key={order.pk} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <input
                          type="checkbox"
                          checked={selectedOrders.includes(order.pk)}
                          onChange={() => handleSelectOrder(order.pk)}
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"
                        />
                        <div className="p-3 bg-orange-50 rounded-lg">
                          <Wrench className="w-6 h-6 text-orange-600" />
                        </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {order.reference}
                          </h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status_text)}`}>
                            {order.status_text}
                          </span>
                        </div>
                        
                        <h4 className="text-md font-medium text-gray-700 mb-1">{order.title}</h4>
                        <p className="text-sm text-gray-600 mb-3">{order.part_detail.description}</p>
                        
                        {/* Progress Bar */}
                        <div className="mb-4">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-sm font-medium text-gray-700">Progress</span>
                            <span className="text-sm text-gray-600">
                              {order.completed} / {order.quantity} completed ({progressPercentage}%)
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(progressPercentage)}`}
                              style={{ width: `${progressPercentage}%` }}
                            ></div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                          <div className="flex items-center space-x-2">
                            <Package className="w-4 h-4 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium text-gray-700">Part</p>
                              <p className="text-sm text-gray-600">{order.part_detail.IPN}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium text-gray-700">Created</p>
                              <p className="text-sm text-gray-600">
                                {format(new Date(order.creation_date), 'MMM dd, yyyy')}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex items-center space-x-2">
                            <Clock className="w-4 h-4 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium text-gray-700">Target Date</p>
                              <p className="text-sm text-gray-600">
                                {format(new Date(order.target_date), 'MMM dd, yyyy')}
                              </p>
                            </div>
                          </div>
                          
                          {order.completion_date && (
                            <div className="flex items-center space-x-2">
                              <CheckCircle className="w-4 h-4 text-green-500" />
                              <div>
                                <p className="text-sm font-medium text-gray-700">Completed</p>
                                <p className="text-sm text-gray-600">
                                  {format(new Date(order.completion_date), 'MMM dd, yyyy')}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {order.notes && (
                          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                            <p className="text-sm text-gray-600">{order.notes}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-1 ml-4">
                      {/* Status Action Buttons */}
                      {order.status_text === 'Pending' && (
                        <Button variant="ghost" size="icon" onClick={() => handleStartBuild(order)} title="Start Build">
                          <Play className="w-4 h-4 text-green-600" />
                        </Button>
                      )}

                      {order.status_text === 'Production' && (
                        <>
                          <Button variant="ghost" size="icon" onClick={() => handlePauseBuild(order)} title="Pause Build">
                            <Pause className="w-4 h-4 text-yellow-600" />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleCompleteBuild(order)} title="Complete Build">
                            <CheckCircle className="w-4 h-4 text-green-600" />
                          </Button>
                        </>
                      )}

                      {/* BOM Management */}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleManageBOM(order.part_detail)}
                        title="Manage BOM"
                      >
                        <Settings className="w-4 h-4" />
                      </Button>

                      {/* Standard Actions */}
                      <Button variant="ghost" size="icon" onClick={() => handleViewBuildOrder(order)}>
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleEditBuildOrder(order)}>
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteBuildOrder(order)}>
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </>
      )}

      {buildOrders.length === 0 && !isLoading && (
        <Card>
          <CardContent className="p-12 text-center">
            <Wrench className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No build orders found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm
                ? 'Try adjusting your search terms'
                : 'Get started by creating your first build order'
              }
            </p>
            <Button onClick={handleCreateBuildOrder}>
              <Plus className="w-4 h-4 mr-2" />
              Create Build Order
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      <BuildOrderForm
        buildOrder={editingBuildOrder || undefined}
        isOpen={isBuildFormOpen}
        onClose={() => {
          setIsBuildFormOpen(false);
          setEditingBuildOrder(null);
        }}
        onSave={handleSaveBuildOrder}
      />

      {selectedPart && (
        <BOMForm
          part={selectedPart}
          isOpen={isBOMFormOpen}
          onClose={() => {
            setIsBOMFormOpen(false);
            setSelectedPart(null);
          }}
          onSave={handleSaveBOM}
        />
      )}
    </div>
  );
};

export default BuildPage;
