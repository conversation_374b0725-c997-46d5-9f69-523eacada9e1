import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Users, Globe, Phone, Mail, Download, Eye, Building } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import CompanyForm from '../../components/companies/CompanyForm';
import CompanyDetail from '../../components/companies/CompanyDetail';

import type { Company } from '../../types';

const CompaniesPage: React.FC = () => {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'supplier' | 'customer' | 'manufacturer'>('all');
  const [selectedCompanies, setSelectedCompanies] = useState<number[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // Modal states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [editingCompany, setEditingCompany] = useState<Company | null>(null);

  useEffect(() => {
    fetchCompanies();
  }, [searchTerm, filterType, filterValues]);

  const fetchCompanies = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for demonstration
      const mockCompanies: Company[] = [
        {
          pk: 1,
          name: 'Digikey Electronics',
          description: 'Electronic components and semiconductors distributor',
          website: 'https://digikey.com',
          address: '701 Brooks Avenue South, Thief River Falls, MN 56701, USA',
          phone: '******-344-4539',
          email: '<EMAIL>',
          contact: 'Sales Team',
          link: '',
          image: '',
          is_customer: false,
          is_supplier: true,
          is_manufacturer: false,
          currency: 'USD',
          active: true,
        },
        {
          pk: 2,
          name: 'ABC Manufacturing Corp',
          description: 'Industrial automation and control systems manufacturer',
          website: 'https://abcmanufacturing.com',
          address: '123 Industrial Blvd, Detroit, MI 48201, USA',
          phone: '******-555-0123',
          email: '<EMAIL>',
          contact: 'John Smith - Procurement Manager',
          link: '',
          image: '',
          is_customer: true,
          is_supplier: false,
          is_manufacturer: true,
          currency: 'USD',
          active: true,
        },
        {
          pk: 3,
          name: 'TechStart Solutions',
          description: 'Technology startup specializing in IoT solutions',
          website: 'https://techstart.io',
          address: '456 Innovation Dr, Austin, TX 78701, USA',
          phone: '******-555-0456',
          email: '<EMAIL>',
          contact: 'Sarah Johnson - CTO',
          link: '',
          image: '',
          is_customer: true,
          is_supplier: false,
          is_manufacturer: false,
          currency: 'USD',
          active: true,
        },
        {
          pk: 4,
          name: 'Global Components Ltd',
          description: 'International electronic components supplier and manufacturer',
          website: 'https://globalcomponents.com',
          address: '789 Commerce St, Hong Kong',
          phone: '+852-1234-5678',
          email: '<EMAIL>',
          contact: 'Michael Chen - Sales Director',
          link: '',
          image: '',
          is_customer: false,
          is_supplier: true,
          is_manufacturer: true,
          currency: 'USD',
          active: true,
        },
      ];

      // Filter by search term and type
      let filteredCompanies = mockCompanies.filter(company =>
        company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        company.contact.toLowerCase().includes(searchTerm.toLowerCase())
      );

      if (filterType !== 'all') {
        filteredCompanies = filteredCompanies.filter(company => {
          switch (filterType) {
            case 'supplier':
              return company.is_supplier;
            case 'customer':
              return company.is_customer;
            case 'manufacturer':
              return company.is_manufacturer;
            default:
              return true;
          }
        });
      }

      setCompanies(filteredCompanies);
    } catch (error) {
      console.error('Failed to fetch companies:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // CRUD Operations
  const handleCreateCompany = () => {
    setEditingCompany(null);
    setIsFormOpen(true);
  };

  const handleViewCompany = (company: Company) => {
    setSelectedCompany(company);
    setIsDetailOpen(true);
  };

  const handleEditCompany = (company: Company) => {
    setEditingCompany(company);
    setIsFormOpen(true);
  };

  const handleDeleteCompany = async (company: Company) => {
    if (window.confirm(`Are you sure you want to delete "${company.name}"?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setCompanies(prev => prev.filter(c => c.pk !== company.pk));

        // Close detail modal if this company was being viewed
        if (selectedCompany?.pk === company.pk) {
          setIsDetailOpen(false);
          setSelectedCompany(null);
        }
      } catch (error) {
        console.error('Failed to delete company:', error);
        alert('Failed to delete company. Please try again.');
      }
    }
  };

  const handleSaveCompany = async (companyData: any) => {
    try {
      if (editingCompany) {
        // Update existing company
        const updatedCompany: Company = {
          ...editingCompany,
          ...companyData,
        };

        setCompanies(prev => prev.map(c => c.pk === editingCompany.pk ? updatedCompany : c));
      } else {
        // Create new company
        const newCompany: Company = {
          pk: Date.now(), // Mock ID
          ...companyData,
          image: '',
        };

        setCompanies(prev => [newCompany, ...prev]);
      }

      setIsFormOpen(false);
      setEditingCompany(null);
    } catch (error) {
      console.error('Failed to save company:', error);
      throw error;
    }
  };

  // Filter operations
  const handleFilterChange = (values: Record<string, any>) => {
    setFilterValues(values);
  };

  const handleFilterApply = () => {
    fetchCompanies();
  };

  const handleFilterClear = () => {
    setFilterValues({});
    fetchCompanies();
  };

  // Bulk operations
  const handleSelectCompany = (companyId: number) => {
    setSelectedCompanies(prev =>
      prev.includes(companyId)
        ? prev.filter(id => id !== companyId)
        : [...prev, companyId]
    );
  };

  const handleSelectAll = () => {
    if (selectedCompanies.length === companies.length) {
      setSelectedCompanies([]);
    } else {
      setSelectedCompanies(companies.map(c => c.pk));
    }
  };

  const handleExport = () => {
    const csvContent = [
      'Name,Description,Email,Phone,Website,Address,Currency,Types,Status',
      ...companies.map(company => {
        const types = getCompanyTypes(company).join(';');
        return `"${company.name}","${company.description}","${company.email}","${company.phone}","${company.website}","${company.address}","${company.currency}","${types}","${company.active ? 'Active' : 'Inactive'}"`;
      })
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'companies_export.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getCompanyTypes = (company: Company) => {
    const types = [];
    if (company.is_supplier) types.push('Supplier');
    if (company.is_customer) types.push('Customer');
    if (company.is_manufacturer) types.push('Manufacturer');
    return types;
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'supplier':
        return 'text-blue-600 bg-blue-50';
      case 'customer':
        return 'text-green-600 bg-green-50';
      case 'manufacturer':
        return 'text-purple-600 bg-purple-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Companies</h1>
          <p className="text-gray-600 mt-2">Manage suppliers, customers, and manufacturers</p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedCompanies.length > 0 && (
            <>
              <Button variant="outline" onClick={() => setSelectedCompanies([])}>
                Clear ({selectedCompanies.length})
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export Selected
              </Button>
            </>
          )}
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
          <Button onClick={handleCreateCompany}>
            <Plus className="w-4 h-4 mr-2" />
            Add Company
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search companies by name, description, or contact..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="supplier">Suppliers</option>
              <option value="customer">Customers</option>
              <option value="manufacturer">Manufacturers</option>
            </select>

           
          </div>

          {selectedCompanies.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedCompanies.length} compan{selectedCompanies.length !== 1 ? 'ies' : 'y'} selected
              </span>
              <Button variant="ghost" size="sm" onClick={() => setSelectedCompanies([])}>
                Clear selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Companies List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Bulk Actions Header */}
          {companies.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedCompanies.length === companies.length && companies.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">
                  {companies.length} compan{companies.length !== 1 ? 'ies' : 'y'} total
                </span>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {companies.map((company) => (
              <Card key={company.pk} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedCompanies.includes(company.pk)}
                        onChange={() => handleSelectCompany(company.pk)}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <Building className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="cursor-pointer" onClick={() => handleViewCompany(company)}>
                        <CardTitle className="text-lg hover:text-primary">{company.name}</CardTitle>
                        <CardDescription>{company.contact}</CardDescription>
                      </div>
                    </div>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="icon" onClick={() => handleViewCompany(company)}>
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleEditCompany(company)}>
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteCompany(company)}>
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{company.description}</p>
                
                {/* Company Types */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {getCompanyTypes(company).map((type) => (
                    <span
                      key={type}
                      className={`px-2 py-1 text-xs rounded-full ${getTypeColor(type)}`}
                    >
                      {type}
                    </span>
                  ))}
                </div>
                
                {/* Contact Information */}
                <div className="space-y-2">
                  {company.website && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Globe className="w-4 h-4 text-gray-400" />
                      <a
                        href={company.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline"
                      >
                        {company.website}
                      </a>
                    </div>
                  )}
                  
                  {company.phone && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-600">{company.phone}</span>
                    </div>
                  )}
                  
                  {company.email && (
                    <div className="flex items-center space-x-2 text-sm">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <a
                        href={`mailto:${company.email}`}
                        className="text-blue-600 hover:underline"
                      >
                        {company.email}
                      </a>
                    </div>
                  )}
                </div>
                
                {/* Address */}
                {company.address && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">{company.address}</p>
                  </div>
                )}
                
                {/* Currency */}
                <div className="mt-3 flex justify-between items-center text-sm">
                  <span className="text-gray-500">Currency:</span>
                  <span className="font-medium">{company.currency}</span>
                </div>
              </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}

      {companies.length === 0 && !isLoading && (
        <Card>
          <CardContent className="p-12 text-center">
            <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No companies found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || filterType !== 'all'
                ? 'Try adjusting your search terms or filters'
                : 'Get started by adding your first company'
              }
            </p>
            <Button onClick={handleCreateCompany}>
              <Plus className="w-4 h-4 mr-2" />
              Add Company
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      <CompanyForm
        company={editingCompany || undefined}
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setEditingCompany(null);
        }}
        onSave={handleSaveCompany}
      />

      {selectedCompany && (
        <CompanyDetail
          company={selectedCompany}
          isOpen={isDetailOpen}
          onClose={() => {
            setIsDetailOpen(false);
            setSelectedCompany(null);
          }}
          onEdit={(company) => {
            setIsDetailOpen(false);
            handleEditCompany(company);
          }}
          onDelete={(company) => {
            setIsDetailOpen(false);
            handleDeleteCompany(company);
          }}
        />
      )}
    </div>
  );
};

export default CompaniesPage;
