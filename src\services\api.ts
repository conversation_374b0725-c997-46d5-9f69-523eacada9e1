import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import type { ApiResponse, ApiError } from '../types/index';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('inventree_token');
        if (token) {
          config.headers.Authorization = `Token ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling (disabled in demo mode)
    // this.api.interceptors.response.use(
    //   (response) => response,
    //   (error) => {
    //     if (error.response?.status === 401) {
    //       // Token expired or invalid
    //       localStorage.removeItem('inventree_token');
    //       window.location.href = '/login';
    //     }
    //     return Promise.reject(error);
    //   }
    // );
  }

  // Generic GET request
  async get<T>(endpoint: string, params?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.get(endpoint, { params });
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  // Generic POST request
  async post<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.post(endpoint, data);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  // Generic PUT request
  async put<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.put(endpoint, data);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  // Generic PATCH request
  async patch<T>(endpoint: string, data?: any): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.patch(endpoint, data);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  // Generic DELETE request
  async delete<T>(endpoint: string): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.api.delete(endpoint);
      return response.data;
    } catch (error: any) {
      throw this.handleError(error);
    }
  }

  // Authentication - Mock implementation for demo
  async login(username: string, password: string): Promise<{ token: string; user: any }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock authentication - accept demo/demo or any username/password
    if ((username === 'demo' && password === 'demo') || username.length > 0) {
      const mockToken = 'mock_jwt_token_' + Date.now();
      const mockUser = {
        id: 1,
        username: username,
        email: `${username}@inventree.demo`,
        first_name: username.charAt(0).toUpperCase() + username.slice(1),
        last_name: 'User',
        is_staff: true,
        is_active: true,
      };

      localStorage.setItem('inventree_token', mockToken);

      return {
        token: mockToken,
        user: mockUser,
      };
    } else {
      throw {
        detail: 'Invalid credentials. Use demo/demo or any username/password.',
      };
    }
  }

  async logout(): Promise<void> {
    // Mock logout - just remove token
    await new Promise(resolve => setTimeout(resolve, 200));
    localStorage.removeItem('inventree_token');
  }

  async getCurrentUser(): Promise<any> {
    // Mock current user - return user based on stored token
    const token = localStorage.getItem('inventree_token');
    if (!token) {
      throw { detail: 'No authentication token found' };
    }

    await new Promise(resolve => setTimeout(resolve, 300));

    return {
      id: 1,
      username: 'demo',
      email: '<EMAIL>',
      first_name: 'Demo',
      last_name: 'User',
      is_staff: true,
      is_active: true,
    };
  }

  // Parts API
  async getParts(params?: any): Promise<ApiResponse<any>> {
    return this.get('/part/', params);
  }

  async getPart(id: number): Promise<any> {
    return this.get(`/part/${id}/`);
  }

  async createPart(data: any): Promise<any> {
    return this.post('/part/', data);
  }

  async updatePart(id: number, data: any): Promise<any> {
    return this.patch(`/part/${id}/`, data);
  }

  async deletePart(id: number): Promise<void> {
    return this.delete(`/part/${id}/`);
  }

  // Stock API
  async getStockItems(params?: any): Promise<ApiResponse<any>> {
    return this.get('/stock/', params);
  }

  async getStockItem(id: number): Promise<any> {
    return this.get(`/stock/${id}/`);
  }

  async createStockItem(data: any): Promise<any> {
    return this.post('/stock/', data);
  }

  async updateStockItem(id: number, data: any): Promise<any> {
    return this.patch(`/stock/${id}/`, data);
  }

  async deleteStockItem(id: number): Promise<void> {
    return this.delete(`/stock/${id}/`);
  }

  // Locations API
  async getLocations(params?: any): Promise<ApiResponse<any>> {
    return this.get('/stock/location/', params);
  }

  // Companies API
  async getCompanies(params?: any): Promise<ApiResponse<any>> {
    return this.get('/company/', params);
  }

  // Purchase Orders API
  async getPurchaseOrders(params?: any): Promise<ApiResponse<any>> {
    return this.get('/order/po/', params);
  }

  // Sales Orders API
  async getSalesOrders(params?: any): Promise<ApiResponse<any>> {
    return this.get('/order/so/', params);
  }

  // Build Orders API
  async getBuildOrders(params?: any): Promise<ApiResponse<any>> {
    return this.get('/build/', params);
  }

  // Dashboard stats
  async getDashboardStats(): Promise<any> {
    return this.get('/stats/');
  }

  private handleError(error: any): ApiError {
    if (error.response) {
      // Server responded with error status
      return {
        detail: error.response.data?.detail || 'An error occurred',
        ...error.response.data,
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        detail: 'Network error - please check your connection',
      };
    } else {
      // Something else happened
      return {
        detail: error.message || 'An unexpected error occurred',
      };
    }
  }
}

export const apiService = new ApiService();
export default apiService;
