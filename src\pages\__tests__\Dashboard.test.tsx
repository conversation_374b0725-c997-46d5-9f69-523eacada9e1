import { render, screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Dashboard from '../Dashboard';

// Mock the API service
vi.mock('../../services/api', () => ({
  default: {
    getDashboardStats: vi.fn(),
  },
}));

describe('Dashboard Component', () => {
  it('renders dashboard title and description', () => {
    render(<Dashboard />);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Welcome to your InvenTree inventory management system')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    render(<Dashboard />);
    
    expect(screen.getByRole('status')).toBeInTheDocument();
  });

  it('displays stats cards after loading', async () => {
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Total Parts')).toBeInTheDocument();
      expect(screen.getByText('Stock Items')).toBeInTheDocument();
      expect(screen.getByText('Low Stock')).toBeInTheDocument();
      expect(screen.getByText('Purchase Orders')).toBeInTheDocument();
      expect(screen.getByText('Sales Orders')).toBeInTheDocument();
      expect(screen.getByText('Build Orders')).toBeInTheDocument();
    });
  });

  it('displays mock data correctly', async () => {
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('1,247')).toBeInTheDocument(); // Total parts
      expect(screen.getByText('3,456')).toBeInTheDocument(); // Stock items
      expect(screen.getByText('23')).toBeInTheDocument(); // Low stock
    });
  });

  it('displays recent activity section', async () => {
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Recent Stock Movements')).toBeInTheDocument();
      expect(screen.getByText('Pending Orders')).toBeInTheDocument();
    });
  });

  it('shows recent stock movements', async () => {
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Resistor 10kΩ')).toBeInTheDocument();
      expect(screen.getByText('Arduino Uno R3')).toBeInTheDocument();
      expect(screen.getByText('LED Red 5mm')).toBeInTheDocument();
    });
  });

  it('shows pending orders', async () => {
    render(<Dashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('PO-2024-001')).toBeInTheDocument();
      expect(screen.getByText('SO-2024-015')).toBeInTheDocument();
      expect(screen.getByText('BO-2024-008')).toBeInTheDocument();
    });
  });
});
