import React, { useState, useEffect } from 'react';
import { Plus, Search, Eye, Edit, ShoppingCart, TrendingUp, Calendar, DollarSign, Download, Trash2 } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import OrderForm from '../../components/orders/OrderForm';
import OrderDetail from '../../components/orders/OrderDetail';
import AdvancedFilter, { orderFilters } from '../../components/filters/AdvancedFilter';
import type { PurchaseOrder, SalesOrder } from '../../types';
import { format } from 'date-fns';

type OrderType = 'purchase' | 'sales';

const OrdersPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<OrderType>('purchase');
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [salesOrders, setSalesOrders] = useState<SalesOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrders, setSelectedOrders] = useState<number[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // Modal states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | SalesOrder | null>(null);
  const [editingOrder, setEditingOrder] = useState<PurchaseOrder | SalesOrder | null>(null);

  useEffect(() => {
    fetchOrders();
  }, [activeTab, searchTerm]);

  const fetchOrders = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for demonstration
      const mockPurchaseOrders: PurchaseOrder[] = [
        {
          pk: 1,
          reference: 'PO-2024-001',
          supplier: 1,
          supplier_detail: {
            pk: 1,
            name: 'Digikey Electronics',
            description: 'Electronic components supplier',
            website: 'https://digikey.com',
            address: '701 Brooks Avenue South, Thief River Falls, MN 56701',
            phone: '******-344-4539',
            email: '<EMAIL>',
            contact: 'Sales Team',
            link: '',
            image: '',
            is_customer: false,
            is_supplier: true,
            is_manufacturer: false,
            currency: 'USD',
            active: true,
          },
          description: 'Arduino boards and sensors for prototype project',
          status: 20,
          status_text: 'Pending',
          creation_date: '2024-01-10T09:00:00Z',
          target_date: '2024-01-20T17:00:00Z',
          complete_date: '',
          line_items: 5,
          total_price: '1234.56',
          currency: 'USD',
        },
        {
          pk: 2,
          reference: 'PO-2024-002',
          supplier: 2,
          supplier_detail: {
            pk: 2,
            name: 'Mouser Electronics',
            description: 'Electronic components and semiconductors',
            website: 'https://mouser.com',
            address: '1000 N Main St, Mansfield, TX 76063',
            phone: '******-346-6873',
            email: '<EMAIL>',
            contact: 'Customer Service',
            link: '',
            image: '',
            is_customer: false,
            is_supplier: true,
            is_manufacturer: false,
            currency: 'USD',
            active: true,
          },
          description: 'Resistors and capacitors bulk order',
          status: 30,
          status_text: 'Placed',
          creation_date: '2024-01-12T14:30:00Z',
          target_date: '2024-01-25T17:00:00Z',
          complete_date: '',
          line_items: 12,
          total_price: '567.89',
          currency: 'USD',
        },
      ];

      const mockSalesOrders: SalesOrder[] = [
        {
          pk: 1,
          reference: 'SO-2024-001',
          customer: 1,
          customer_detail: {
            pk: 1,
            name: 'ABC Manufacturing Corp',
            description: 'Industrial automation solutions',
            website: 'https://abcmanufacturing.com',
            address: '123 Industrial Blvd, Detroit, MI 48201',
            phone: '******-555-0123',
            email: '<EMAIL>',
            contact: 'John Smith',
            link: '',
            image: '',
            is_customer: true,
            is_supplier: false,
            is_manufacturer: true,
            currency: 'USD',
            active: true,
          },
          description: 'Custom sensor modules for production line',
          status: 10,
          status_text: 'Pending',
          creation_date: '2024-01-08T11:15:00Z',
          target_date: '2024-01-30T17:00:00Z',
          shipment_date: '',
          line_items: 3,
          total_price: '2456.78',
          currency: 'USD',
        },
        {
          pk: 2,
          reference: 'SO-2024-002',
          customer: 2,
          customer_detail: {
            pk: 2,
            name: 'TechStart Solutions',
            description: 'Technology startup',
            website: 'https://techstart.io',
            address: '456 Innovation Dr, Austin, TX 78701',
            phone: '******-555-0456',
            email: '<EMAIL>',
            contact: 'Sarah Johnson',
            link: '',
            image: '',
            is_customer: true,
            is_supplier: false,
            is_manufacturer: false,
            currency: 'USD',
            active: true,
          },
          description: 'Development kits and prototyping materials',
          status: 20,
          status_text: 'In Progress',
          creation_date: '2024-01-14T16:45:00Z',
          target_date: '2024-02-05T17:00:00Z',
          shipment_date: '',
          line_items: 8,
          total_price: '890.12',
          currency: 'USD',
        },
      ];

      if (activeTab === 'purchase') {
        const filtered = mockPurchaseOrders.filter(order =>
          order.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.supplier_detail.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setPurchaseOrders(filtered);
      } else {
        const filtered = mockSalesOrders.filter(order =>
          order.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.customer_detail.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          order.description.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setSalesOrders(filtered);
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'placed':
      case 'in progress':
        return 'text-blue-600 bg-blue-50';
      case 'complete':
      case 'shipped':
        return 'text-green-600 bg-green-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // CRUD Operations
  const handleCreateOrder = () => {
    setEditingOrder(null);
    setIsFormOpen(true);
  };

  const handleViewOrder = (order: PurchaseOrder | SalesOrder) => {
    setSelectedOrder(order);
    setIsDetailOpen(true);
  };

  const handleEditOrder = (order: PurchaseOrder | SalesOrder) => {
    setEditingOrder(order);
    setIsFormOpen(true);
  };

  const handleDeleteOrder = async (order: PurchaseOrder | SalesOrder) => {
    if (window.confirm(`Are you sure you want to delete order "${order.reference}"?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));

        if (activeTab === 'purchase') {
          setPurchaseOrders(prev => prev.filter(o => o.pk !== order.pk));
        } else {
          setSalesOrders(prev => prev.filter(o => o.pk !== order.pk));
        }

        // Close detail modal if this order was being viewed
        if (selectedOrder?.pk === order.pk) {
          setIsDetailOpen(false);
          setSelectedOrder(null);
        }
      } catch (error) {
        console.error('Failed to delete order:', error);
        alert('Failed to delete order. Please try again.');
      }
    }
  };

  const handleSaveOrder = async (orderData: any) => {
    try {
      if (editingOrder) {
        // Update existing order
        const updatedOrder = {
          ...editingOrder,
          ...orderData,
          line_items: orderData.line_items.length,
          total_price: orderData.line_items.reduce((sum: number, item: any) => sum + (item.quantity * item.unit_price), 0).toString(),
        };

        if (activeTab === 'purchase') {
          setPurchaseOrders(prev => prev.map(o => o.pk === editingOrder.pk ? updatedOrder as PurchaseOrder : o));
        } else {
          setSalesOrders(prev => prev.map(o => o.pk === editingOrder.pk ? updatedOrder as SalesOrder : o));
        }
      } else {
        // Create new order
        const newOrder = {
          pk: Date.now(),
          ...orderData,
          status: 10,
          status_text: 'Pending',
          creation_date: new Date().toISOString(),
          complete_date: '',
          line_items: orderData.line_items.length,
          total_price: orderData.line_items.reduce((sum: number, item: any) => sum + (item.quantity * item.unit_price), 0).toString(),
          currency: 'USD',
        };

        if (activeTab === 'purchase') {
          // Add supplier detail for purchase orders
          const supplier = mockSuppliers.find(s => s.pk === orderData.company);
          (newOrder as PurchaseOrder).supplier = orderData.company;
          (newOrder as PurchaseOrder).supplier_detail = supplier!;
          setPurchaseOrders(prev => [newOrder as PurchaseOrder, ...prev]);
        } else {
          // Add customer detail for sales orders
          const customer = mockCustomers.find(c => c.pk === orderData.company);
          (newOrder as SalesOrder).customer = orderData.company;
          (newOrder as SalesOrder).customer_detail = customer!;
          (newOrder as SalesOrder).shipment_date = '';
          setSalesOrders(prev => [newOrder as SalesOrder, ...prev]);
        }
      }

      setIsFormOpen(false);
      setEditingOrder(null);
    } catch (error) {
      console.error('Failed to save order:', error);
      throw error;
    }
  };

  const handleStatusChange = async (order: PurchaseOrder | SalesOrder, newStatus: number) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));

      const statusText = getStatusText(newStatus);
      const updatedOrder = { ...order, status: newStatus, status_text: statusText };

      if (activeTab === 'purchase') {
        setPurchaseOrders(prev => prev.map(o => o.pk === order.pk ? updatedOrder as PurchaseOrder : o));
      } else {
        setSalesOrders(prev => prev.map(o => o.pk === order.pk ? updatedOrder as SalesOrder : o));
      }

      // Update selected order if it's being viewed
      if (selectedOrder?.pk === order.pk) {
        setSelectedOrder(updatedOrder);
      }
    } catch (error) {
      console.error('Failed to update order status:', error);
    }
  };

  const getStatusText = (status: number): string => {
    switch (status) {
      case 10: return 'Pending';
      case 20: return activeTab === 'purchase' ? 'Placed' : 'In Progress';
      case 30: return activeTab === 'purchase' ? 'Complete' : 'Shipped';
      case 40: return 'Cancelled';
      default: return 'Unknown';
    }
  };

  // Mock data for companies
  const mockSuppliers = [
    { pk: 1, name: 'Digikey Electronics', description: 'Electronic components supplier', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: false, is_supplier: true, is_manufacturer: false, currency: 'USD', active: true },
    { pk: 2, name: 'Mouser Electronics', description: 'Electronic components supplier', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: false, is_supplier: true, is_manufacturer: false, currency: 'USD', active: true },
  ];

  const mockCustomers = [
    { pk: 3, name: 'ABC Manufacturing Corp', description: 'Industrial customer', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: true, is_supplier: false, is_manufacturer: true, currency: 'USD', active: true },
    { pk: 4, name: 'TechStart Solutions', description: 'Technology startup', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: true, is_supplier: false, is_manufacturer: false, currency: 'USD', active: true },
  ];

  // Filter operations
  const handleFilterChange = (values: Record<string, any>) => {
    setFilterValues(values);
  };

  const handleFilterApply = () => {
    fetchOrders();
  };

  const handleFilterClear = () => {
    setFilterValues({});
    fetchOrders();
  };

  // Bulk operations
  const handleSelectOrder = (orderId: number) => {
    setSelectedOrders(prev =>
      prev.includes(orderId)
        ? prev.filter(id => id !== orderId)
        : [...prev, orderId]
    );
  };

  const handleSelectAll = () => {
    const currentOrders = activeTab === 'purchase' ? purchaseOrders : salesOrders;
    if (selectedOrders.length === currentOrders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(currentOrders.map(o => o.pk));
    }
  };

  const handleExport = () => {
    const currentOrders = activeTab === 'purchase' ? purchaseOrders : salesOrders;
    const csvContent = [
      'Reference,Company,Description,Status,Creation Date,Target Date,Total,Currency',
      ...currentOrders.map(order => {
        const company = activeTab === 'purchase'
          ? (order as PurchaseOrder).supplier_detail.name
          : (order as SalesOrder).customer_detail.name;
        return `"${order.reference}","${company}","${order.description}","${order.status_text}","${order.creation_date}","${order.target_date}","${order.total_price}","${order.currency}"`;
      })
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${activeTab}_orders_export.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const currentOrders = activeTab === 'purchase' ? purchaseOrders : salesOrders;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
          <p className="text-gray-600 mt-2">Manage purchase and sales orders</p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedOrders.length > 0 && (
            <>
              <Button variant="outline" onClick={() => setSelectedOrders([])}>
                Clear ({selectedOrders.length})
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export Selected
              </Button>
            </>
          )}
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
          <Button onClick={handleCreateOrder}>
            <Plus className="w-4 h-4 mr-2" />
            Create {activeTab === 'purchase' ? 'Purchase' : 'Sales'} Order
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('purchase')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'purchase'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <ShoppingCart className="w-4 h-4 inline mr-2" />
            Purchase Orders
          </button>
          <button
            onClick={() => setActiveTab('sales')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'sales'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <TrendingUp className="w-4 h-4 inline mr-2" />
            Sales Orders
          </button>
        </nav>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder={`Search ${activeTab} orders...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <AdvancedFilter
              title="Order Filters"
              description="Filter orders by various criteria"
              filters={orderFilters}
              values={filterValues}
              onChange={handleFilterChange}
              onApply={handleFilterApply}
              onClear={handleFilterClear}
            />
          </div>

          {selectedOrders.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedOrders.length} order{selectedOrders.length !== 1 ? 's' : ''} selected
              </span>
              <Button variant="ghost" size="sm" onClick={() => setSelectedOrders([])}>
                Clear selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Orders List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Bulk Actions Header */}
          {currentOrders.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedOrders.length === currentOrders.length && currentOrders.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">
                  {currentOrders.length} {activeTab} order{currentOrders.length !== 1 ? 's' : ''} total
                </span>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {currentOrders.map((order) => (
              <Card key={order.pk} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <input
                        type="checkbox"
                        checked={selectedOrders.includes(order.pk)}
                        onChange={() => handleSelectOrder(order.pk)}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"
                      />
                      <div className="p-3 bg-blue-50 rounded-lg">
                        {activeTab === 'purchase' ? (
                          <ShoppingCart className="w-6 h-6 text-blue-600" />
                        ) : (
                          <TrendingUp className="w-6 h-6 text-blue-600" />
                        )}
                      </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {order.reference}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status_text)}`}>
                          {order.status_text}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{order.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">
                            {activeTab === 'purchase' ? 'Supplier' : 'Customer'}
                          </p>
                          <p className="text-sm text-gray-600">
                            {activeTab === 'purchase' 
                              ? (order as PurchaseOrder).supplier_detail.name
                              : (order as SalesOrder).customer_detail.name
                            }
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-700">Created</p>
                            <p className="text-sm text-gray-600">
                              {format(new Date(order.creation_date), 'MMM dd, yyyy')}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-700">Target Date</p>
                            <p className="text-sm text-gray-600">
                              {format(new Date(order.target_date), 'MMM dd, yyyy')}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <DollarSign className="w-4 h-4 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-700">Total</p>
                            <p className="text-lg font-semibold text-green-600">
                              ${parseFloat(order.total_price).toLocaleString()}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="mt-3">
                        <p className="text-sm text-gray-500">
                          {order.line_items} line item{order.line_items !== 1 ? 's' : ''}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-1 ml-4">
                    <Button variant="ghost" size="icon" onClick={() => handleViewOrder(order)}>
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleEditOrder(order)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDeleteOrder(order)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        </>
      )}

      {currentOrders.length === 0 && !isLoading && (
        <Card>
          <CardContent className="p-12 text-center">
            {activeTab === 'purchase' ? (
              <ShoppingCart className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            ) : (
              <TrendingUp className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            )}
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No {activeTab} orders found
            </h3>
            <p className="text-gray-500 mb-4">
              {searchTerm
                ? 'Try adjusting your search terms'
                : `Get started by creating your first ${activeTab} order`
              }
            </p>
            <Button onClick={handleCreateOrder}>
              <Plus className="w-4 h-4 mr-2" />
              Create {activeTab === 'purchase' ? 'Purchase' : 'Sales'} Order
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      <OrderForm
        order={editingOrder || undefined}
        orderType={activeTab}
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setEditingOrder(null);
        }}
        onSave={handleSaveOrder}
      />

      {selectedOrder && (
        <OrderDetail
          order={selectedOrder}
          orderType={activeTab}
          isOpen={isDetailOpen}
          onClose={() => {
            setIsDetailOpen(false);
            setSelectedOrder(null);
          }}
          onEdit={(order) => {
            setIsDetailOpen(false);
            handleEditOrder(order);
          }}
          onDelete={(order) => {
            setIsDetailOpen(false);
            handleDeleteOrder(order);
          }}
          onStatusChange={handleStatusChange}
        />
      )}
    </div>
  );
};

export default OrdersPage;
