// Core types for InvenTree system

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_active: boolean;
}

export interface Part {
  pk: number;
  name: string;
  description: string;
  category: number;
  category_name?: string;
  IPN: string; // Internal Part Number
  revision: string;
  keywords: string;
  link: string;
  image: string;
  default_location: number;
  default_supplier: number;
  minimum_stock: number;
  units: string;
  salable: boolean;
  assembly: boolean;
  component: boolean;
  purchaseable: boolean;
  trackable: boolean;
  active: boolean;
  virtual: boolean;
  in_stock: number;
  stock_item_count: number;
  building: number;
  can_build: number;
}

export interface StockItem {
  pk: number;
  part: number;
  part_detail: Part;
  location: number;
  location_name?: string;
  quantity: number;
  allocated: number;
  available: number;
  serial: string;
  batch: string;
  status: number;
  status_text: string;
  notes: string;
  updated: string;
  stocktake_date: string;
  expiry_date: string;
  link: string;
}

export interface Location {
  pk: number;
  name: string;
  description: string;
  parent: number;
  pathstring: string;
  level: number;
  tree_id: number;
  lft: number;
  rght: number;
  items: number;
}

export interface Company {
  pk: number;
  name: string;
  description: string;
  website: string;
  address: string;
  phone: string;
  email: string;
  contact: string;
  link: string;
  image: string;
  is_customer: boolean;
  is_supplier: boolean;
  is_manufacturer: boolean;
  currency: string;
  active: boolean;
}

export interface PurchaseOrder {
  pk: number;
  reference: string;
  supplier: number;
  supplier_detail: Company;
  description: string;
  status: number;
  status_text: string;
  creation_date: string;
  target_date: string;
  complete_date: string;
  line_items: number;
  total_price: string;
  currency: string;
}

export interface SalesOrder {
  pk: number;
  reference: string;
  customer: number;
  customer_detail: Company;
  description: string;
  status: number;
  status_text: string;
  creation_date: string;
  target_date: string;
  shipment_date: string;
  line_items: number;
  total_price: string;
  currency: string;
}

export interface BuildOrder {
  pk: number;
  reference: string;
  title: string;
  part: number;
  part_detail: Part;
  quantity: number;
  completed: number;
  status: number;
  status_text: string;
  creation_date: string;
  target_date: string;
  completion_date: string;
  notes: string;
  link: string;
}

export interface BOMItem {
  pk: number;
  part: number;
  part_detail: Part;
  sub_part: number;
  sub_part_detail: Part;
  quantity: number;
  reference: string;
  note: string;
  checksum: string;
  inherited: boolean;
  optional: boolean;
  consumable: boolean;
  overage: string;
  validated: boolean;
  substitutes: any[];
}

// API Response types
export interface ApiResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface ApiError {
  detail?: string;
  non_field_errors?: string[];
  [key: string]: any;
}

// Form types
export interface LoginForm {
  username: string;
  password: string;
}

export interface PartForm {
  name: string;
  description: string;
  category: number;
  IPN: string;
  keywords: string;
  link: string;
  minimum_stock: number;
  units: string;
  salable: boolean;
  assembly: boolean;
  component: boolean;
  purchaseable: boolean;
  trackable: boolean;
  active: boolean;
}

export interface StockItemForm {
  part: number;
  location: number;
  quantity: number;
  serial: string;
  batch: string;
  notes: string;
  expiry_date: string;
}

// Navigation types
export interface NavItem {
  title: string;
  href: string;
  icon?: string;
  children?: NavItem[];
}

// Dashboard types
export interface DashboardStats {
  total_parts: number;
  total_stock_items: number;
  low_stock_count: number;
  total_purchase_orders: number;
  total_sales_orders: number;
  total_build_orders: number;
}
