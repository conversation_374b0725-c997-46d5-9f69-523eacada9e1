import React, { useEffect, useState } from 'react';
import { Package, Warehouse, ShoppingCart, TrendingUp, AlertTriangle, Wrench } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/Card';
import type { DashboardStats } from '../types';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // For demo purposes, we'll use mock data since the API might not be available
        const mockStats: DashboardStats = {
          total_parts: 1247,
          total_stock_items: 3456,
          low_stock_count: 23,
          total_purchase_orders: 45,
          total_sales_orders: 67,
          total_build_orders: 12,
        };
        
        // Simulate API call delay
        setTimeout(() => {
          setStats(mockStats);
          setIsLoading(false);
        }, 1000);
        
        // Uncomment this when you have a real API
        // const data = await apiService.getDashboardStats();
        // setStats(data);
      } catch (error) {
        console.error('Failed to fetch dashboard stats:', error);
        setIsLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const statCards = [
    {
      title: 'Total Parts',
      value: stats?.total_parts || 0,
      icon: Package,
      description: 'Active parts in system',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Stock Items',
      value: stats?.total_stock_items || 0,
      icon: Warehouse,
      description: 'Items in inventory',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Low Stock',
      value: stats?.low_stock_count || 0,
      icon: AlertTriangle,
      description: 'Items below minimum',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Purchase Orders',
      value: stats?.total_purchase_orders || 0,
      icon: ShoppingCart,
      description: 'Active POs',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Sales Orders',
      value: stats?.total_sales_orders || 0,
      icon: TrendingUp,
      description: 'Active SOs',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Build Orders',
      value: stats?.total_build_orders || 0,
      icon: Wrench,
      description: 'Active builds',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Welcome to your InvenTree inventory management system</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`w-4 h-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Stock Movements</CardTitle>
            <CardDescription>Latest inventory changes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { part: 'Resistor 10kΩ', action: 'Added', quantity: '+100', time: '2 hours ago' },
                { part: 'Arduino Uno R3', action: 'Removed', quantity: '-5', time: '4 hours ago' },
                { part: 'LED Red 5mm', action: 'Added', quantity: '+50', time: '6 hours ago' },
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                  <div>
                    <p className="font-medium">{item.part}</p>
                    <p className="text-sm text-gray-500">{item.action} • {item.time}</p>
                  </div>
                  <span className={`font-medium ${item.quantity.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {item.quantity}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pending Orders</CardTitle>
            <CardDescription>Orders requiring attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { order: 'PO-2024-001', supplier: 'Digikey', status: 'Pending', amount: '$1,234.56' },
                { order: 'SO-2024-015', customer: 'ABC Corp', status: 'Processing', amount: '$2,456.78' },
                { order: 'BO-2024-008', product: 'Custom PCB', status: 'In Progress', amount: '$890.12' },
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                  <div>
                    <p className="font-medium">{item.order}</p>
                    <p className="text-sm text-gray-500">{item.supplier || item.customer || item.product}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{item.amount}</p>
                    <p className="text-sm text-gray-500">{item.status}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
