import React, { useState, useEffect } from 'react';
import { X, Save, Upload, Link, Package } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { Part, PartForm as PartFormData } from '../../types';

interface PartFormProps {
  part?: Part;
  isOpen: boolean;
  onClose: () => void;
  onSave: (partData: PartFormData) => Promise<void>;
}

const PartForm: React.FC<PartFormProps> = ({ part, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState<PartFormData>({
    name: '',
    description: '',
    category: 0,
    IPN: '',
    keywords: '',
    link: '',
    minimum_stock: 0,
    units: 'pcs',
    salable: false,
    assembly: false,
    component: true,
    purchaseable: true,
    trackable: false,
    active: true,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const categories = [
    { id: 1, name: 'Microcontrollers' },
    { id: 2, name: 'Resistors' },
    { id: 3, name: 'Capacitors' },
    { id: 4, name: 'LEDs' },
    { id: 5, name: 'Sensors' },
    { id: 6, name: 'Connectors' },
    { id: 7, name: 'ICs' },
    { id: 8, name: 'Mechanical' },
  ];

  const units = ['pcs', 'kg', 'g', 'm', 'cm', 'mm', 'L', 'mL', 'set', 'pair'];

  useEffect(() => {
    if (part) {
      setFormData({
        name: part.name,
        description: part.description,
        category: part.category,
        IPN: part.IPN,
        keywords: part.keywords,
        link: part.link,
        minimum_stock: part.minimum_stock,
        units: part.units,
        salable: part.salable,
        assembly: part.assembly,
        component: part.component,
        purchaseable: part.purchaseable,
        trackable: part.trackable,
        active: part.active,
      });
    } else {
      // Reset form for new part
      setFormData({
        name: '',
        description: '',
        category: 0,
        IPN: '',
        keywords: '',
        link: '',
        minimum_stock: 0,
        units: 'pcs',
        salable: false,
        assembly: false,
        component: true,
        purchaseable: true,
        trackable: false,
        active: true,
      });
    }
    setErrors({});
  }, [part, isOpen]);

  const handleInputChange = (field: keyof PartFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Part name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.IPN.trim()) {
      newErrors.IPN = 'Internal Part Number is required';
    }

    if (formData.category === 0) {
      newErrors.category = 'Please select a category';
    }

    if (formData.minimum_stock < 0) {
      newErrors.minimum_stock = 'Minimum stock cannot be negative';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Failed to save part:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">
                  {part ? 'Edit Part' : 'Create New Part'}
                </h2>
                <p className="text-sm text-gray-500">
                  {part ? 'Update part information' : 'Add a new part to inventory'}
                </p>
              </div>
            </div>
            <Button type="button" variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Essential part details and identification</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Part Name *
                    </label>
                    <Input
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="Enter part name"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Internal Part Number (IPN) *
                    </label>
                    <Input
                      value={formData.IPN}
                      onChange={(e) => handleInputChange('IPN', e.target.value)}
                      placeholder="Enter IPN"
                      className={errors.IPN ? 'border-red-500' : ''}
                    />
                    {errors.IPN && <p className="text-red-500 text-xs mt-1">{errors.IPN}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter detailed description"
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Category *
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', parseInt(e.target.value))}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                        errors.category ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value={0}>Select category</option>
                      {categories.map((cat) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                    </select>
                    {errors.category && <p className="text-red-500 text-xs mt-1">{errors.category}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Units
                    </label>
                    <select
                      value={formData.units}
                      onChange={(e) => handleInputChange('units', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      {units.map((unit) => (
                        <option key={unit} value={unit}>
                          {unit}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Minimum Stock
                    </label>
                    <Input
                      type="number"
                      value={formData.minimum_stock}
                      onChange={(e) => handleInputChange('minimum_stock', parseInt(e.target.value) || 0)}
                      placeholder="0"
                      min="0"
                      className={errors.minimum_stock ? 'border-red-500' : ''}
                    />
                    {errors.minimum_stock && <p className="text-red-500 text-xs mt-1">{errors.minimum_stock}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Keywords
                    </label>
                    <Input
                      value={formData.keywords}
                      onChange={(e) => handleInputChange('keywords', e.target.value)}
                      placeholder="Enter keywords (comma separated)"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      External Link
                    </label>
                    <Input
                      value={formData.link}
                      onChange={(e) => handleInputChange('link', e.target.value)}
                      placeholder="https://..."
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Part Properties */}
            <Card>
              <CardHeader>
                <CardTitle>Part Properties</CardTitle>
                <CardDescription>Configure part behavior and capabilities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {[
                    { key: 'active', label: 'Active', description: 'Part is active and available' },
                    { key: 'component', label: 'Component', description: 'Part can be used in assemblies' },
                    { key: 'assembly', label: 'Assembly', description: 'Part can be built from components' },
                    { key: 'purchaseable', label: 'Purchaseable', description: 'Part can be purchased' },
                    { key: 'salable', label: 'Salable', description: 'Part can be sold' },
                    { key: 'trackable', label: 'Trackable', description: 'Track individual items by serial/batch' },
                  ].map((prop) => (
                    <div key={prop.key} className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        id={prop.key}
                        checked={formData[prop.key as keyof PartFormData] as boolean}
                        onChange={(e) => handleInputChange(prop.key as keyof PartFormData, e.target.checked)}
                        className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <div>
                        <label htmlFor={prop.key} className="text-sm font-medium text-gray-700 cursor-pointer">
                          {prop.label}
                        </label>
                        <p className="text-xs text-gray-500">{prop.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {part ? 'Update Part' : 'Create Part'}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PartForm;
