import React, { useState } from 'react';
import { FileText, Download, Calendar, Filter, BarChart3, <PERSON>Chart, TrendingUp, Package, ShoppingCart, Users, Building, Wrench, DollarSign, AlertTriangle } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ComponentType<{ className?: string }>;
  lastGenerated?: string;
}

const ReportsPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedReport, setSelectedReport] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });
  const [isGenerating, setIsGenerating] = useState(false);

  const reportTemplates: ReportTemplate[] = [
    // Inventory Reports
    {
      id: 'stock-levels',
      name: 'Stock Levels Report',
      description: 'Current stock quantities and locations for all parts',
      category: 'inventory',
      icon: Package,
      lastGenerated: '2024-01-15T10:30:00Z',
    },
    {
      id: 'low-stock-alert',
      name: 'Low Stock Alert',
      description: 'Parts below minimum stock levels requiring attention',
      category: 'inventory',
      icon: AlertTriangle,
      lastGenerated: '2024-01-15T09:15:00Z',
    },
    {
      id: 'stock-movements',
      name: 'Stock Movements',
      description: 'Historical stock transactions and adjustments',
      category: 'inventory',
      icon: BarChart3,
      lastGenerated: '2024-01-14T14:20:00Z',
    },
    {
      id: 'part-usage-analysis',
      name: 'Part Usage Analysis',
      description: 'Most and least used parts with consumption patterns',
      category: 'inventory',
      icon: TrendingUp,
      lastGenerated: '2024-01-14T11:45:00Z',
    },
    {
      id: 'inventory-valuation',
      name: 'Inventory Valuation',
      description: 'Total inventory value and cost breakdown',
      category: 'inventory',
      icon: DollarSign,
      lastGenerated: '2024-01-13T16:30:00Z',
    },

    // Order Reports
    {
      id: 'purchase-orders',
      name: 'Purchase Orders Summary',
      description: 'Purchase order status, suppliers, and delivery performance',
      category: 'orders',
      icon: ShoppingCart,
      lastGenerated: '2024-01-13T14:20:00Z',
    },
    {
      id: 'sales-orders',
      name: 'Sales Orders Analysis',
      description: 'Sales performance, customer trends, and revenue analysis',
      category: 'orders',
      icon: TrendingUp,
      lastGenerated: '2024-01-12T16:45:00Z',
    },
    {
      id: 'order-fulfillment',
      name: 'Order Fulfillment Report',
      description: 'Delivery performance and fulfillment metrics',
      category: 'orders',
      icon: BarChart3,
      lastGenerated: '2024-01-12T13:15:00Z',
    },
    {
      id: 'supplier-performance',
      name: 'Supplier Performance',
      description: 'Supplier delivery times, quality, and reliability metrics',
      category: 'orders',
      icon: PieChart,
      lastGenerated: '2024-01-11T15:30:00Z',
    },

    // Manufacturing Reports
    {
      id: 'build-orders',
      name: 'Build Orders Status',
      description: 'Manufacturing order status and completion rates',
      category: 'manufacturing',
      icon: Wrench,
      lastGenerated: '2024-01-11T11:30:00Z',
    },
    {
      id: 'production-efficiency',
      name: 'Production Efficiency',
      description: 'Manufacturing performance and efficiency metrics',
      category: 'manufacturing',
      icon: BarChart3,
      lastGenerated: '2024-01-10T14:45:00Z',
    },
    {
      id: 'bom-analysis',
      name: 'BOM Analysis',
      description: 'Bill of materials cost analysis and component availability',
      category: 'manufacturing',
      icon: FileText,
      lastGenerated: '2024-01-10T10:20:00Z',
    },
    {
      id: 'work-in-progress',
      name: 'Work in Progress',
      description: 'Current manufacturing pipeline and WIP inventory',
      category: 'manufacturing',
      icon: TrendingUp,
      lastGenerated: '2024-01-09T16:15:00Z',
    },

    // Company Reports
    {
      id: 'supplier-summary',
      name: 'Supplier Summary',
      description: 'Comprehensive supplier relationship overview',
      category: 'companies',
      icon: Building,
      lastGenerated: '2024-01-09T13:00:00Z',
    },
    {
      id: 'customer-analysis',
      name: 'Customer Analysis',
      description: 'Customer order patterns, preferences, and value analysis',
      category: 'companies',
      icon: Users,
      lastGenerated: '2024-01-08T15:45:00Z',
    },
    {
      id: 'vendor-comparison',
      name: 'Vendor Comparison',
      description: 'Comparative analysis of supplier performance and costs',
      category: 'companies',
      icon: BarChart3,
      lastGenerated: '2024-01-08T12:30:00Z',
    },

    // Financial Reports
    {
      id: 'cost-analysis',
      name: 'Cost Analysis',
      description: 'Part costs, pricing trends, and cost optimization opportunities',
      category: 'financial',
      icon: DollarSign,
      lastGenerated: '2024-01-07T14:15:00Z',
    },
    {
      id: 'profit-margins',
      name: 'Profit Margins',
      description: 'Product profitability analysis and margin trends',
      category: 'financial',
      icon: TrendingUp,
      lastGenerated: '2024-01-07T11:20:00Z',
    },
    {
      id: 'budget-variance',
      name: 'Budget Variance',
      description: 'Actual vs budgeted spending analysis',
      category: 'financial',
      icon: BarChart3,
      lastGenerated: '2024-01-06T16:00:00Z',
    },
  ];

  const categories = [
    { value: 'all', label: 'All Categories', count: reportTemplates.length },
    { value: 'inventory', label: 'Inventory', count: reportTemplates.filter(r => r.category === 'inventory').length },
    { value: 'orders', label: 'Orders', count: reportTemplates.filter(r => r.category === 'orders').length },
    { value: 'manufacturing', label: 'Manufacturing', count: reportTemplates.filter(r => r.category === 'manufacturing').length },
    { value: 'companies', label: 'Companies', count: reportTemplates.filter(r => r.category === 'companies').length },
    { value: 'financial', label: 'Financial', count: reportTemplates.filter(r => r.category === 'financial').length },
  ];

  const filteredReports = reportTemplates.filter(report => {
    const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const handleGenerateReport = async (reportId: string) => {
    setSelectedReport(reportId);
    setIsGenerating(true);

    try {
      // Mock report generation delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('Generated report:', reportId);
    } catch (error) {
      console.error('Failed to generate report:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownloadReport = (reportId: string, format: 'pdf' | 'excel' | 'csv' = 'pdf') => {
    const report = reportTemplates.find(r => r.id === reportId);
    if (!report) return;

    // Mock download functionality
    const filename = `${report.name.replace(/\s+/g, '_')}_${dateRange.start}_to_${dateRange.end}.${format}`;
    const blob = new Blob(['Mock report data'], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);

    console.log(`Downloaded ${report.name} as ${format}`);
  };

  const handleScheduleReport = (reportId: string) => {
    console.log('Schedule report:', reportId);
    // TODO: Implement report scheduling
  };

  const handleCustomReport = () => {
    console.log('Create custom report');
    // TODO: Implement custom report builder
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'inventory':
        return 'text-blue-600 bg-blue-50';
      case 'orders':
        return 'text-green-600 bg-green-50';
      case 'manufacturing':
        return 'text-orange-600 bg-orange-50';
      case 'companies':
        return 'text-purple-600 bg-purple-50';
      case 'financial':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Mock report data for preview
  const getMockReportData = (reportId: string) => {
    const mockData: Record<string, any> = {
      'stock-levels': {
        title: 'Current Stock Levels',
        data: [
          { part: 'Arduino Uno R3', current: 25, minimum: 10, location: 'Main Warehouse' },
          { part: 'Resistor 10kΩ', current: 450, minimum: 100, location: 'Main Warehouse' },
          { part: 'LED Red 5mm', current: 89, minimum: 50, location: 'Production Floor' },
          { part: 'Capacitor 100µF', current: 67, minimum: 25, location: 'Main Warehouse' },
          { part: 'PCB Main Board', current: 15, minimum: 20, location: 'Assembly Area' },
        ],
      },
      'low-stock-alert': {
        title: 'Low Stock Alert',
        data: [
          { part: 'PCB Main Board', current: 15, minimum: 20, status: 'Critical', days_remaining: 3 },
          { part: 'Enclosure Plastic', current: 8, minimum: 15, status: 'Low', days_remaining: 7 },
          { part: 'Power Connector', current: 12, minimum: 25, status: 'Low', days_remaining: 5 },
        ],
      },
      'purchase-orders': {
        title: 'Purchase Orders Summary',
        data: [
          { reference: 'PO-2024-001', supplier: 'Digikey Electronics', status: 'Complete', total: '$1,250.00', date: '2024-01-15' },
          { reference: 'PO-2024-002', supplier: 'Mouser Electronics', status: 'Pending', total: '$850.00', date: '2024-01-18' },
          { reference: 'PO-2024-003', supplier: 'Digikey Electronics', status: 'Shipped', total: '$2,100.00', date: '2024-01-20' },
        ],
      },
    };

    return mockData[reportId] || null;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
          <p className="text-gray-600 mt-2">Generate comprehensive reports and analyze your inventory data</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => handleScheduleReport('')}>
            <Calendar className="w-4 h-4 mr-2" />
            Schedule Report
          </Button>
          <Button onClick={handleCustomReport}>
            <FileText className="w-4 h-4 mr-2" />
            Custom Report
          </Button>
        </div>
      </div>

      {/* Date Range Selector */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <Input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <Input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              />
            </div>
            <div className="flex items-end">
              <Button variant="outline">
                Apply Date Range
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label} ({category.count})
                </option>
              ))}
            </select>
            
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              Date Range
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Reports</p>
                <p className="text-2xl font-bold text-gray-900">{reportTemplates.length}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Generated Today</p>
                <p className="text-2xl font-bold text-gray-900">3</p>
              </div>
              <BarChart3 className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled Reports</p>
                <p className="text-2xl font-bold text-gray-900">5</p>
              </div>
              <Calendar className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Downloads</p>
                <p className="text-2xl font-bold text-gray-900">127</p>
              </div>
              <Download className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredReports.map((report) => (
          <Card key={report.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <report.icon className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{report.name}</CardTitle>
                    <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${getCategoryColor(report.category)}`}>
                      {report.category}
                    </span>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-4">
                {report.description}
              </CardDescription>
              
              {report.lastGenerated && (
                <p className="text-sm text-gray-500 mb-4">
                  Last generated: {new Date(report.lastGenerated).toLocaleDateString()}
                </p>
              )}
              
              <div className="flex space-x-2">
                <Button 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleGenerateReport(report.id)}
                >
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Generate
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleDownloadReport(report.id)}
                >
                  <Download className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReports.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No reports found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || selectedCategory !== 'all'
                ? 'Try adjusting your search terms or filters'
                : 'No reports available'
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Report Preview */}
      {selectedReport && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Report Preview</CardTitle>
                <CardDescription>
                  {reportTemplates.find(r => r.id === selectedReport)?.name}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                {isGenerating ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                    <span className="text-sm text-gray-600">Generating...</span>
                  </div>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadReport(selectedReport, 'pdf')}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      PDF
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadReport(selectedReport, 'excel')}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      Excel
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadReport(selectedReport, 'csv')}
                    >
                      <Download className="w-4 h-4 mr-1" />
                      CSV
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {(() => {
              const reportData = getMockReportData(selectedReport);
              if (!reportData) {
                return (
                  <div className="text-center py-12">
                    <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Report Preview</h3>
                    <p className="text-gray-500">
                      Report data will be displayed here. This is a preview of the selected report.
                    </p>
                  </div>
                );
              }

              return (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">{reportData.title}</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-50">
                          {Object.keys(reportData.data[0] || {}).map((key) => (
                            <th key={key} className="border border-gray-300 px-4 py-2 text-left capitalize">
                              {key.replace(/_/g, ' ')}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {reportData.data.map((item: any, index: number) => (
                          <tr key={index} className="hover:bg-gray-50">
                            {Object.entries(item).map(([key, value]: [string, any]) => (
                              <td key={key} className="border border-gray-300 px-4 py-2">
                                {key === 'status' ? (
                                  <span className={`px-2 py-1 text-xs rounded-full ${
                                    value === 'Critical'
                                      ? 'bg-red-50 text-red-600'
                                      : value === 'Low'
                                      ? 'bg-yellow-50 text-yellow-600'
                                      : value === 'Complete'
                                      ? 'bg-green-50 text-green-600'
                                      : value === 'Pending'
                                      ? 'bg-yellow-50 text-yellow-600'
                                      : 'bg-blue-50 text-blue-600'
                                  }`}>
                                    {value}
                                  </span>
                                ) : (
                                  value
                                )}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              );
            })()}
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Report Activity</CardTitle>
          <CardDescription>Latest report generations and downloads</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { report: 'Stock Level Report', action: 'Generated', user: 'John Doe', time: '2 hours ago' },
              { report: 'Purchase Orders Summary', action: 'Downloaded', user: 'Jane Smith', time: '4 hours ago' },
              { report: 'Inventory Valuation Report', action: 'Generated', user: 'Mike Johnson', time: '6 hours ago' },
              { report: 'Supplier Performance Report', action: 'Scheduled', user: 'System', time: '1 day ago' },
            ].map((activity, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    <FileText className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-medium">{activity.report}</p>
                    <p className="text-sm text-gray-500">{activity.action} by {activity.user}</p>
                  </div>
                </div>
                <span className="text-sm text-gray-500">{activity.time}</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ReportsPage;
