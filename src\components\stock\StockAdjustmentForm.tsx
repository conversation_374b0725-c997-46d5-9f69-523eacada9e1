import React, { useState } from 'react';
import { X, Save, Plus, Minus, Package, MapPin } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { StockItem } from '../../types';

interface StockAdjustmentFormProps {
  stockItem: StockItem;
  isOpen: boolean;
  onClose: () => void;
  onSave: (adjustment: StockAdjustment) => Promise<void>;
}

interface StockAdjustment {
  type: 'add' | 'remove' | 'count' | 'move';
  quantity: number;
  notes: string;
  location?: number;
  reason: string;
}

const StockAdjustmentForm: React.FC<StockAdjustmentFormProps> = ({
  stockItem,
  isOpen,
  onClose,
  onSave,
}) => {
  const [adjustment, setAdjustment] = useState<StockAdjustment>({
    type: 'add',
    quantity: 0,
    notes: '',
    reason: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const adjustmentTypes = [
    { value: 'add', label: 'Add Stock', icon: Plus, color: 'text-green-600' },
    { value: 'remove', label: 'Remove Stock', icon: Minus, color: 'text-red-600' },
    { value: 'count', label: 'Stock Count', icon: Package, color: 'text-blue-600' },
    { value: 'move', label: 'Move Stock', icon: MapPin, color: 'text-purple-600' },
  ];

  const reasons = [
    'Production consumption',
    'Damaged/defective',
    'Lost/missing',
    'Returned to supplier',
    'Customer return',
    'Inventory correction',
    'Quality control',
    'Expired/obsolete',
    'Transfer between locations',
    'Other',
  ];

  const locations = [
    { id: 1, name: 'Main Warehouse - A1' },
    { id: 2, name: 'Production Floor' },
    { id: 3, name: 'Quality Control' },
    { id: 4, name: 'Shipping Area' },
    { id: 5, name: 'Returns Area' },
  ];

  const handleInputChange = (field: keyof StockAdjustment, value: any) => {
    setAdjustment(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (adjustment.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0';
    }

    if (adjustment.type === 'remove' && adjustment.quantity > stockItem.available) {
      newErrors.quantity = `Cannot remove more than available quantity (${stockItem.available})`;
    }

    if (!adjustment.reason.trim()) {
      newErrors.reason = 'Reason is required';
    }

    if (adjustment.type === 'move' && !adjustment.location) {
      newErrors.location = 'Destination location is required for moves';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(adjustment);
      onClose();
    } catch (error) {
      console.error('Failed to save stock adjustment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getResultingQuantity = () => {
    switch (adjustment.type) {
      case 'add':
        return stockItem.quantity + adjustment.quantity;
      case 'remove':
        return stockItem.quantity - adjustment.quantity;
      case 'count':
        return adjustment.quantity;
      case 'move':
        return stockItem.quantity; // Quantity stays same, just location changes
      default:
        return stockItem.quantity;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Package className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Stock Adjustment</h2>
                <p className="text-sm text-gray-500">{stockItem.part_detail.name}</p>
              </div>
            </div>
            <Button type="button" variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* Current Stock Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current Stock</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-sm text-gray-600">Total</p>
                    <p className="text-2xl font-bold text-gray-900">{stockItem.quantity}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Available</p>
                    <p className="text-2xl font-bold text-green-600">{stockItem.available}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Allocated</p>
                    <p className="text-2xl font-bold text-orange-600">{stockItem.allocated}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Adjustment Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Adjustment Type
              </label>
              <div className="grid grid-cols-2 gap-3">
                {adjustmentTypes.map((type) => (
                  <button
                    key={type.value}
                    type="button"
                    onClick={() => handleInputChange('type', type.value)}
                    className={`p-3 border rounded-lg flex items-center space-x-3 transition-colors ${
                      adjustment.type === type.value
                        ? 'border-primary bg-primary/5 text-primary'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <type.icon className={`w-5 h-5 ${type.color}`} />
                    <span className="font-medium">{type.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {adjustment.type === 'count' ? 'New Quantity' : 'Quantity'}
              </label>
              <Input
                type="number"
                value={adjustment.quantity}
                onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                placeholder="Enter quantity"
                min="0"
                className={errors.quantity ? 'border-red-500' : ''}
              />
              {errors.quantity && <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>}
              
              {adjustment.quantity > 0 && (
                <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">
                    Resulting quantity: <span className="font-medium">{getResultingQuantity()}</span>
                  </p>
                </div>
              )}
            </div>

            {/* Location (for moves) */}
            {adjustment.type === 'move' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Destination Location
                </label>
                <select
                  value={adjustment.location || ''}
                  onChange={(e) => handleInputChange('location', parseInt(e.target.value))}
                  className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.location ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Select destination location</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </select>
                {errors.location && <p className="text-red-500 text-xs mt-1">{errors.location}</p>}
              </div>
            )}

            {/* Reason */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason
              </label>
              <select
                value={adjustment.reason}
                onChange={(e) => handleInputChange('reason', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.reason ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select reason</option>
                {reasons.map((reason) => (
                  <option key={reason} value={reason}>
                    {reason}
                  </option>
                ))}
              </select>
              {errors.reason && <p className="text-red-500 text-xs mt-1">{errors.reason}</p>}
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes (Optional)
              </label>
              <textarea
                value={adjustment.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Additional notes about this adjustment"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Apply Adjustment
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockAdjustmentForm;
