import React, { useState, useEffect } from 'react';
import { X, Save, Plus, Trash2, Package, Calculator, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { Part } from '../../types';

interface BOMFormProps {
  part?: Part;
  isOpen: boolean;
  onClose: () => void;
  onSave: (bomData: BOMData) => Promise<void>;
}

interface BOMItem {
  id?: number;
  sub_part: number;
  sub_part_name: string;
  sub_part_ipn: string;
  quantity: number;
  reference: string;
  overage: number;
  inherited: boolean;
  optional: boolean;
  consumable: boolean;
  allow_variants: boolean;
  note: string;
}

interface BOMData {
  part: number;
  items: BOMItem[];
}

const BOMForm: React.FC<BOMFormProps> = ({ part, isOpen, onClose, onSave }) => {
  const [bomData, setBomData] = useState<BOMData>({
    part: part?.pk || 0,
    items: [],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mock parts data
  const availableParts = [
    { pk: 1, name: 'Arduino Uno R3', IPN: 'ARD-UNO-R3', units: 'pcs', in_stock: 25 },
    { pk: 2, name: 'Resistor 10kΩ', IPN: 'RES-10K-0.25W', units: 'pcs', in_stock: 450 },
    { pk: 3, name: 'LED Red 5mm', IPN: 'LED-RED-5MM', units: 'pcs', in_stock: 89 },
    { pk: 4, name: 'Capacitor 100µF', IPN: 'CAP-100UF-16V', units: 'pcs', in_stock: 67 },
    { pk: 5, name: 'PCB Main Board', IPN: 'PCB-MAIN-V1', units: 'pcs', in_stock: 15 },
    { pk: 6, name: 'Enclosure Plastic', IPN: 'ENC-PLASTIC-001', units: 'pcs', in_stock: 30 },
  ];

  useEffect(() => {
    if (part && isOpen) {
      // Load existing BOM if editing
      setBomData({
        part: part.pk,
        items: [
          // Mock existing BOM items
          {
            sub_part: 1,
            sub_part_name: 'Arduino Uno R3',
            sub_part_ipn: 'ARD-UNO-R3',
            quantity: 1,
            reference: 'U1',
            overage: 0,
            inherited: false,
            optional: false,
            consumable: false,
            allow_variants: false,
            note: 'Main microcontroller',
          },
          {
            sub_part: 2,
            sub_part_name: 'Resistor 10kΩ',
            sub_part_ipn: 'RES-10K-0.25W',
            quantity: 4,
            reference: 'R1-R4',
            overage: 10,
            inherited: false,
            optional: false,
            consumable: true,
            allow_variants: true,
            note: 'Pull-up resistors',
          },
        ],
      });
    }
    setErrors({});
  }, [part, isOpen]);

  const addBOMItem = () => {
    const newItem: BOMItem = {
      sub_part: 0,
      sub_part_name: '',
      sub_part_ipn: '',
      quantity: 1,
      reference: '',
      overage: 0,
      inherited: false,
      optional: false,
      consumable: false,
      allow_variants: false,
      note: '',
    };
    setBomData(prev => ({
      ...prev,
      items: [...prev.items, newItem],
    }));
  };

  const updateBOMItem = (index: number, field: keyof BOMItem, value: any) => {
    setBomData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => {
        if (i === index) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'sub_part') {
            const selectedPart = availableParts.find(p => p.pk === value);
            if (selectedPart) {
              updatedItem.sub_part_name = selectedPart.name;
              updatedItem.sub_part_ipn = selectedPart.IPN;
            }
          }
          return updatedItem;
        }
        return item;
      }),
    }));
  };

  const removeBOMItem = (index: number) => {
    setBomData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const calculateTotalQuantity = (item: BOMItem): number => {
    return item.quantity + Math.ceil(item.quantity * (item.overage / 100));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (bomData.items.length === 0) {
      newErrors.items = 'At least one BOM item is required';
    }

    // Validate individual BOM items
    bomData.items.forEach((item, index) => {
      if (item.sub_part === 0) {
        newErrors[`item_${index}_part`] = 'Part is required';
      }
      if (item.quantity <= 0) {
        newErrors[`item_${index}_quantity`] = 'Quantity must be greater than 0';
      }
      if (item.overage < 0) {
        newErrors[`item_${index}_overage`] = 'Overage cannot be negative';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(bomData);
      onClose();
    } catch (error) {
      console.error('Failed to save BOM:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-7xl w-full max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-50 rounded-lg">
                <Package className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Bill of Materials</h2>
                <p className="text-sm text-gray-500">
                  {part ? `${part.name} (${part.IPN})` : 'Manage BOM items'}
                </p>
              </div>
            </div>
            <Button type="button" variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* BOM Items */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>BOM Items</CardTitle>
                    <CardDescription>Components required to build this assembly</CardDescription>
                  </div>
                  <Button type="button" variant="outline" onClick={addBOMItem}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Item
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {bomData.items.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 mb-4">No BOM items added yet</p>
                    <Button type="button" variant="outline" onClick={addBOMItem}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add First Item
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {bomData.items.map((item, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="grid grid-cols-1 lg:grid-cols-6 gap-4">
                          {/* Part Selection */}
                          <div className="lg:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Part *
                            </label>
                            <select
                              value={item.sub_part}
                              onChange={(e) => updateBOMItem(index, 'sub_part', parseInt(e.target.value))}
                              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                                errors[`item_${index}_part`] ? 'border-red-500' : 'border-gray-300'
                              }`}
                            >
                              <option value={0}>Select part</option>
                              {availableParts.map((part) => (
                                <option key={part.pk} value={part.pk}>
                                  {part.name} ({part.IPN})
                                </option>
                              ))}
                            </select>
                            {errors[`item_${index}_part`] && (
                              <p className="text-red-500 text-xs mt-1">{errors[`item_${index}_part`]}</p>
                            )}
                          </div>

                          {/* Quantity */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Quantity *
                            </label>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => updateBOMItem(index, 'quantity', parseInt(e.target.value) || 0)}
                              min="1"
                              className={errors[`item_${index}_quantity`] ? 'border-red-500' : ''}
                            />
                            {errors[`item_${index}_quantity`] && (
                              <p className="text-red-500 text-xs mt-1">{errors[`item_${index}_quantity`]}</p>
                            )}
                          </div>

                          {/* Reference */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Reference
                            </label>
                            <Input
                              value={item.reference}
                              onChange={(e) => updateBOMItem(index, 'reference', e.target.value)}
                              placeholder="e.g., R1, U1"
                            />
                          </div>

                          {/* Overage */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Overage (%)
                            </label>
                            <Input
                              type="number"
                              value={item.overage}
                              onChange={(e) => updateBOMItem(index, 'overage', parseInt(e.target.value) || 0)}
                              min="0"
                              max="100"
                              className={errors[`item_${index}_overage`] ? 'border-red-500' : ''}
                            />
                            {errors[`item_${index}_overage`] && (
                              <p className="text-red-500 text-xs mt-1">{errors[`item_${index}_overage`]}</p>
                            )}
                          </div>

                          {/* Total Quantity */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Total Needed
                            </label>
                            <div className="px-3 py-2 bg-gray-50 border rounded-md flex items-center">
                              <Calculator className="w-4 h-4 text-gray-400 mr-2" />
                              <span className="font-medium">{calculateTotalQuantity(item)}</span>
                            </div>
                          </div>
                        </div>

                        {/* Properties */}
                        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                          {[
                            { key: 'optional', label: 'Optional' },
                            { key: 'consumable', label: 'Consumable' },
                            { key: 'inherited', label: 'Inherited' },
                            { key: 'allow_variants', label: 'Allow Variants' },
                          ].map((prop) => (
                            <div key={prop.key} className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id={`${index}_${prop.key}`}
                                checked={item[prop.key as keyof BOMItem] as boolean}
                                onChange={(e) => updateBOMItem(index, prop.key as keyof BOMItem, e.target.checked)}
                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              />
                              <label htmlFor={`${index}_${prop.key}`} className="text-sm text-gray-700">
                                {prop.label}
                              </label>
                            </div>
                          ))}
                        </div>

                        {/* Note */}
                        <div className="mt-4">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Note
                          </label>
                          <Input
                            value={item.note}
                            onChange={(e) => updateBOMItem(index, 'note', e.target.value)}
                            placeholder="Additional notes about this item"
                          />
                        </div>

                        {/* Stock Warning */}
                        {item.sub_part > 0 && (() => {
                          const selectedPart = availableParts.find(p => p.pk === item.sub_part);
                          const totalNeeded = calculateTotalQuantity(item);
                          const inStock = selectedPart?.in_stock || 0;
                          
                          if (inStock < totalNeeded) {
                            return (
                              <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center space-x-2">
                                <AlertTriangle className="w-4 h-4 text-yellow-600" />
                                <span className="text-sm text-yellow-800">
                                  Insufficient stock: {inStock} available, {totalNeeded} needed
                                </span>
                              </div>
                            );
                          }
                          return null;
                        })()}

                        {/* Remove Button */}
                        <div className="mt-4 flex justify-end">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeBOMItem(index)}
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            Remove Item
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* BOM Summary */}
                    <div className="border-t pt-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 mb-2">BOM Summary</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Total Items:</span>
                            <span className="ml-2 font-medium">{bomData.items.length}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Total Components:</span>
                            <span className="ml-2 font-medium">
                              {bomData.items.reduce((sum, item) => sum + calculateTotalQuantity(item), 0)}
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-600">Optional Items:</span>
                            <span className="ml-2 font-medium">
                              {bomData.items.filter(item => item.optional).length}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {errors.items && <p className="text-red-500 text-xs mt-2">{errors.items}</p>}
              </CardContent>
            </Card>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || bomData.items.length === 0}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save BOM
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BOMForm;
