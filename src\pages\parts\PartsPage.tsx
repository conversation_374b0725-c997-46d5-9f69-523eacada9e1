import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Package, Eye, Download, Grid, List } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import PartForm from '../../components/parts/PartForm';
import PartDetail from '../../components/parts/PartDetail';
import AdvancedFilter, { partsFilters } from '../../components/filters/AdvancedFilter';
import type { Part, PartForm as PartFormData } from '../../types';

const PartsPage: React.FC = () => {
  const [parts, setParts] = useState<Part[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedParts, setSelectedParts] = useState<number[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // Modal states
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDetailOpen, setIsDetailOpen] = useState(false);
  const [selectedPart, setSelectedPart] = useState<Part | null>(null);
  const [editingPart, setEditingPart] = useState<Part | null>(null);

  useEffect(() => {
    fetchParts();
  }, [currentPage, searchTerm, filterValues]);

  const fetchParts = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for demonstration
      const mockParts: Part[] = [
        {
          pk: 1,
          name: 'Arduino Uno R3',
          description: 'Microcontroller board based on the ATmega328P',
          category: 1,
          category_name: 'Microcontrollers',
          IPN: 'ARD-UNO-R3',
          revision: 'R3',
          keywords: 'arduino, microcontroller, atmega328p',
          link: 'https://arduino.cc/en/Main/ArduinoBoardUno',
          image: '',
          default_location: 1,
          default_supplier: 1,
          minimum_stock: 10,
          units: 'pcs',
          salable: true,
          assembly: false,
          component: true,
          purchaseable: true,
          trackable: true,
          active: true,
          virtual: false,
          in_stock: 25,
          stock_item_count: 3,
          building: 0,
          can_build: 0,
        },
        {
          pk: 2,
          name: 'Resistor 10kΩ',
          description: '10k Ohm resistor, 1/4W, 5% tolerance',
          category: 2,
          category_name: 'Resistors',
          IPN: 'RES-10K-0.25W',
          revision: '1.0',
          keywords: 'resistor, 10k, 10000, ohm',
          link: '',
          image: '',
          default_location: 2,
          default_supplier: 2,
          minimum_stock: 100,
          units: 'pcs',
          salable: true,
          assembly: false,
          component: true,
          purchaseable: true,
          trackable: false,
          active: true,
          virtual: false,
          in_stock: 450,
          stock_item_count: 5,
          building: 0,
          can_build: 0,
        },
        {
          pk: 3,
          name: 'LED Red 5mm',
          description: 'Red LED, 5mm diameter, 20mA forward current',
          category: 3,
          category_name: 'LEDs',
          IPN: 'LED-RED-5MM',
          revision: '1.0',
          keywords: 'led, red, 5mm, light',
          link: '',
          image: '',
          default_location: 3,
          default_supplier: 1,
          minimum_stock: 50,
          units: 'pcs',
          salable: true,
          assembly: false,
          component: true,
          purchaseable: true,
          trackable: false,
          active: true,
          virtual: false,
          in_stock: 75,
          stock_item_count: 2,
          building: 0,
          can_build: 0,
        },
      ];

      // Filter by search term
      const filteredParts = mockParts.filter(part =>
        part.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        part.IPN.toLowerCase().includes(searchTerm.toLowerCase())
      );

      setParts(filteredParts);
      setTotalCount(filteredParts.length);
      
      // Uncomment when you have a real API
      // const response: ApiResponse<Part> = await apiService.getParts({
      //   search: searchTerm,
      //   page: currentPage,
      // });
      // setParts(response.results);
      // setTotalCount(response.count);
    } catch (error) {
      console.error('Failed to fetch parts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // CRUD Operations
  const handleCreatePart = () => {
    setEditingPart(null);
    setIsFormOpen(true);
  };

  const handleEditPart = (part: Part) => {
    setEditingPart(part);
    setIsFormOpen(true);
  };

  const handleViewPart = (part: Part) => {
    setSelectedPart(part);
    setIsDetailOpen(true);
  };

  const handleDeletePart = async (part: Part) => {
    if (window.confirm(`Are you sure you want to delete "${part.name}"?`)) {
      try {
        // Mock delete operation
        await new Promise(resolve => setTimeout(resolve, 500));
        setParts(prev => prev.filter(p => p.pk !== part.pk));
        setTotalCount(prev => prev - 1);

        // Close detail modal if this part was being viewed
        if (selectedPart?.pk === part.pk) {
          setIsDetailOpen(false);
          setSelectedPart(null);
        }
      } catch (error) {
        console.error('Failed to delete part:', error);
        alert('Failed to delete part. Please try again.');
      }
    }
  };

  const handleSavePart = async (partData: PartFormData) => {
    try {
      if (editingPart) {
        // Update existing part
        const updatedPart: Part = {
          ...editingPart,
          ...partData,
          category_name: getCategoryName(partData.category),
        };

        setParts(prev => prev.map(p => p.pk === editingPart.pk ? updatedPart : p));
      } else {
        // Create new part
        const newPart: Part = {
          pk: Date.now(), // Mock ID
          ...partData,
          category_name: getCategoryName(partData.category),
          revision: '1.0',
          image: '',
          default_location: 1,
          default_supplier: 1,
          virtual: false,
          in_stock: 0,
          stock_item_count: 0,
          building: 0,
          can_build: 0,
        };

        setParts(prev => [newPart, ...prev]);
        setTotalCount(prev => prev + 1);
      }

      setIsFormOpen(false);
      setEditingPart(null);
    } catch (error) {
      console.error('Failed to save part:', error);
      throw error;
    }
  };

  const getCategoryName = (categoryId: number): string => {
    const categories = [
      { id: 1, name: 'Microcontrollers' },
      { id: 2, name: 'Resistors' },
      { id: 3, name: 'Capacitors' },
      { id: 4, name: 'LEDs' },
      { id: 5, name: 'Sensors' },
      { id: 6, name: 'Connectors' },
      { id: 7, name: 'ICs' },
      { id: 8, name: 'Mechanical' },
    ];
    return categories.find(cat => cat.id === categoryId)?.name || 'Unknown';
  };

  // Filter operations
  const handleFilterChange = (values: Record<string, any>) => {
    setFilterValues(values);
  };

  const handleFilterApply = () => {
    fetchParts();
  };

  const handleFilterClear = () => {
    setFilterValues({});
    fetchParts();
  };

  // Bulk operations
  const handleSelectPart = (partId: number) => {
    setSelectedParts(prev =>
      prev.includes(partId)
        ? prev.filter(id => id !== partId)
        : [...prev, partId]
    );
  };

  const handleSelectAll = () => {
    if (selectedParts.length === parts.length) {
      setSelectedParts([]);
    } else {
      setSelectedParts(parts.map(p => p.pk));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedParts.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedParts.length} parts?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setParts(prev => prev.filter(p => !selectedParts.includes(p.pk)));
        setTotalCount(prev => prev - selectedParts.length);
        setSelectedParts([]);
      } catch (error) {
        console.error('Failed to delete parts:', error);
        alert('Failed to delete parts. Please try again.');
      }
    }
  };

  const handleExport = () => {
    // Mock export functionality
    const csvContent = [
      'Name,IPN,Description,Category,In Stock,Units',
      ...parts.map(part =>
        `"${part.name}","${part.IPN}","${part.description}","${part.category_name}",${part.in_stock},"${part.units}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'parts_export.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStockStatusColor = (part: Part) => {
    if (part.in_stock <= 0) return 'text-red-600';
    if (part.in_stock <= part.minimum_stock) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Parts</h1>
          <p className="text-gray-600 mt-2">Manage your inventory parts and components</p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedParts.length > 0 && (
            <>
              <Button variant="outline" onClick={handleBulkDelete}>
                <Trash2 className="w-4 h-4 mr-2" />
                Delete ({selectedParts.length})
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </>
          )}
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
          <Button onClick={handleCreatePart}>
            <Plus className="w-4 h-4 mr-2" />
            Add Part
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search parts by name, description, or IPN..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <AdvancedFilter
              title="Part Filters"
              description="Filter parts by various criteria"
              filters={partsFilters}
              values={filterValues}
              onChange={handleFilterChange}
              onApply={handleFilterApply}
              onClear={handleFilterClear}
            />

            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="icon"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {selectedParts.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedParts.length} part{selectedParts.length !== 1 ? 's' : ''} selected
              </span>
              <Button variant="ghost" size="sm" onClick={() => setSelectedParts([])}>
                Clear selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Parts List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Bulk Actions Header */}
          {parts.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedParts.length === parts.length && parts.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">
                  {totalCount} part{totalCount !== 1 ? 's' : ''} total
                </span>
              </div>
              <div className="text-sm text-gray-500">
                Page {currentPage}
              </div>
            </div>
          )}

          <div className={viewMode === 'grid'
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
          }>
            {parts.map((part) => (
              <Card key={part.pk} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={selectedParts.includes(part.pk)}
                        onChange={() => handleSelectPart(part.pk)}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <Package className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="cursor-pointer" onClick={() => handleViewPart(part)}>
                        <CardTitle className="text-lg hover:text-primary">{part.name}</CardTitle>
                        <CardDescription>{part.IPN}</CardDescription>
                      </div>
                    </div>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="icon" onClick={() => handleViewPart(part)}>
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleEditPart(part)}>
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => handleDeletePart(part)}>
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{part.description}</p>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Category:</span>
                    <span>{part.category_name}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">In Stock:</span>
                    <span className={`font-medium ${getStockStatusColor(part)}`}>
                      {part.in_stock} {part.units}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Min Stock:</span>
                    <span>{part.minimum_stock} {part.units}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Stock Items:</span>
                    <span>{part.stock_item_count}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-1 mt-4">
                  {part.purchaseable && (
                    <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                      Purchaseable
                    </span>
                  )}
                  {part.salable && (
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      Salable
                    </span>
                  )}
                  {part.assembly && (
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">
                      Assembly
                    </span>
                  )}
                  {part.trackable && (
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">
                      Trackable
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        </>
      )}

      {parts.length === 0 && !isLoading && (
        <Card>
          <CardContent className="p-12 text-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No parts found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first part'}
            </p>
            <Button onClick={handleCreatePart}>
              <Plus className="w-4 h-4 mr-2" />
              Add Part
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      <PartForm
        part={editingPart || undefined}
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setEditingPart(null);
        }}
        onSave={handleSavePart}
      />

      {selectedPart && (
        <PartDetail
          part={selectedPart}
          isOpen={isDetailOpen}
          onClose={() => {
            setIsDetailOpen(false);
            setSelectedPart(null);
          }}
          onEdit={(part) => {
            setIsDetailOpen(false);
            handleEditPart(part);
          }}
          onDelete={(part) => {
            setIsDetailOpen(false);
            handleDeletePart(part);
          }}
        />
      )}
    </div>
  );
};

export default PartsPage;
