import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Warehouse, MapPin, Calendar, ArrowRightLeft, Download, Eye, Settings } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import StockAdjustmentForm from '../../components/stock/StockAdjustmentForm';
import StockTransferForm from '../../components/stock/StockTransferForm';
import AdvancedFilter, { stockFilters } from '../../components/filters/AdvancedFilter';
import type { StockItem } from '../../types';
import { format } from 'date-fns';

const StockPage: React.FC = () => {
  const [stockItems, setStockItems] = useState<StockItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // Modal states
  const [isAdjustmentOpen, setIsAdjustmentOpen] = useState(false);
  const [isTransferOpen, setIsTransferOpen] = useState(false);
  const [selectedStockItem, setSelectedStockItem] = useState<StockItem | null>(null);

  useEffect(() => {
    fetchStockItems();
  }, [searchTerm]);

  const fetchStockItems = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for demonstration
      const mockStockItems: StockItem[] = [
        {
          pk: 1,
          part: 1,
          part_detail: {
            pk: 1,
            name: 'Arduino Uno R3',
            description: 'Microcontroller board based on the ATmega328P',
            category: 1,
            IPN: 'ARD-UNO-R3',
            revision: 'R3',
            keywords: 'arduino, microcontroller',
            link: '',
            image: '',
            default_location: 1,
            default_supplier: 1,
            minimum_stock: 10,
            units: 'pcs',
            salable: true,
            assembly: false,
            component: true,
            purchaseable: true,
            trackable: true,
            active: true,
            virtual: false,
            in_stock: 25,
            stock_item_count: 3,
            building: 0,
            can_build: 0,
          },
          location: 1,
          location_name: 'Electronics Storage - Shelf A1',
          quantity: 15,
          allocated: 2,
          available: 13,
          serial: 'ARD001-ARD015',
          batch: 'B2024-001',
          status: 10,
          status_text: 'OK',
          notes: 'Received from Digikey order PO-2024-001',
          updated: '2024-01-15T10:30:00Z',
          stocktake_date: '2024-01-10T09:00:00Z',
          expiry_date: '',
          link: '',
        },
        {
          pk: 2,
          part: 2,
          part_detail: {
            pk: 2,
            name: 'Resistor 10kΩ',
            description: '10k Ohm resistor, 1/4W, 5% tolerance',
            category: 2,
            IPN: 'RES-10K-0.25W',
            revision: '1.0',
            keywords: 'resistor, 10k',
            link: '',
            image: '',
            default_location: 2,
            default_supplier: 2,
            minimum_stock: 100,
            units: 'pcs',
            salable: true,
            assembly: false,
            component: true,
            purchaseable: true,
            trackable: false,
            active: true,
            virtual: false,
            in_stock: 450,
            stock_item_count: 5,
            building: 0,
            can_build: 0,
          },
          location: 2,
          location_name: 'Components - Drawer B2',
          quantity: 200,
          allocated: 0,
          available: 200,
          serial: '',
          batch: 'R10K-2024-A',
          status: 10,
          status_text: 'OK',
          notes: 'Bulk purchase for production',
          updated: '2024-01-12T14:20:00Z',
          stocktake_date: '2024-01-08T16:00:00Z',
          expiry_date: '',
          link: '',
        },
        {
          pk: 3,
          part: 3,
          part_detail: {
            pk: 3,
            name: 'LED Red 5mm',
            description: 'Red LED, 5mm diameter, 20mA forward current',
            category: 3,
            IPN: 'LED-RED-5MM',
            revision: '1.0',
            keywords: 'led, red, 5mm',
            link: '',
            image: '',
            default_location: 3,
            default_supplier: 1,
            minimum_stock: 50,
            units: 'pcs',
            salable: true,
            assembly: false,
            component: true,
            purchaseable: true,
            trackable: false,
            active: true,
            virtual: false,
            in_stock: 75,
            stock_item_count: 2,
            building: 0,
            can_build: 0,
          },
          location: 3,
          location_name: 'Components - Drawer C1',
          quantity: 50,
          allocated: 5,
          available: 45,
          serial: '',
          batch: 'LED-R-2024-001',
          status: 10,
          status_text: 'OK',
          notes: 'Standard red LEDs for indicators',
          updated: '2024-01-14T11:45:00Z',
          stocktake_date: '2024-01-09T13:30:00Z',
          expiry_date: '',
          link: '',
        },
      ];

      // Filter by search term
      const filteredItems = mockStockItems.filter(item =>
        item.part_detail.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.part_detail.IPN.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.location_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.batch.toLowerCase().includes(searchTerm.toLowerCase())
      );

      setStockItems(filteredItems);
    } catch (error) {
      console.error('Failed to fetch stock items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // CRUD Operations
  const handleCreateStockItem = () => {
    console.log('Create new stock item');
    // TODO: Implement stock item creation
  };

  const handleEditStockItem = (item: StockItem) => {
    console.log('Edit stock item:', item);
    // TODO: Implement stock item editing
  };

  const handleDeleteStockItem = async (item: StockItem) => {
    if (window.confirm(`Are you sure you want to delete this stock item?`)) {
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        setStockItems(prev => prev.filter(si => si.pk !== item.pk));
      } catch (error) {
        console.error('Failed to delete stock item:', error);
        alert('Failed to delete stock item. Please try again.');
      }
    }
  };

  // Stock Operations
  const handleStockAdjustment = (item: StockItem) => {
    setSelectedStockItem(item);
    setIsAdjustmentOpen(true);
  };

  const handleStockTransfer = () => {
    setIsTransferOpen(true);
  };

  const handleSaveAdjustment = async (adjustment: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      // Update stock item based on adjustment
      setStockItems(prev => prev.map(item => {
        if (item.pk === selectedStockItem?.pk) {
          let newQuantity = item.quantity;
          switch (adjustment.type) {
            case 'add':
              newQuantity += adjustment.quantity;
              break;
            case 'remove':
              newQuantity -= adjustment.quantity;
              break;
            case 'count':
              newQuantity = adjustment.quantity;
              break;
          }

          return {
            ...item,
            quantity: Math.max(0, newQuantity),
            available: Math.max(0, newQuantity - item.allocated),
            updated: new Date().toISOString(),
          };
        }
        return item;
      }));

      setIsAdjustmentOpen(false);
      setSelectedStockItem(null);
    } catch (error) {
      console.error('Failed to save adjustment:', error);
      throw error;
    }
  };

  const handleSaveTransfer = async (transfer: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update stock items based on transfer
      setStockItems(prev => prev.map(item => {
        const transferItem = transfer.items.find((ti: any) => ti.stockItemId === item.pk);
        if (transferItem) {
          return {
            ...item,
            quantity: item.quantity - transferItem.quantity,
            available: item.available - transferItem.quantity,
            updated: new Date().toISOString(),
          };
        }
        return item;
      }));

      setIsTransferOpen(false);
    } catch (error) {
      console.error('Failed to save transfer:', error);
      throw error;
    }
  };

  // Filter operations
  const handleFilterChange = (values: Record<string, any>) => {
    setFilterValues(values);
  };

  const handleFilterApply = () => {
    fetchStockItems();
  };

  const handleFilterClear = () => {
    setFilterValues({});
    fetchStockItems();
  };

  // Bulk operations
  const handleSelectItem = (itemId: number) => {
    setSelectedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === stockItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(stockItems.map(item => item.pk));
    }
  };

  const handleExport = () => {
    const csvContent = [
      'Part Name,IPN,Location,Quantity,Available,Allocated,Batch,Status,Last Updated',
      ...stockItems.map(item =>
        `"${item.part_detail.name}","${item.part_detail.IPN}","${item.location_name}",${item.quantity},${item.available},${item.allocated},"${item.batch}","${item.status_text}","${item.updated}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'stock_export.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'ok':
        return 'text-green-600 bg-green-50';
      case 'damaged':
        return 'text-red-600 bg-red-50';
      case 'lost':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-blue-600 bg-blue-50';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Stock Items</h1>
          <p className="text-gray-600 mt-2">Manage your inventory stock locations and quantities</p>
        </div>
        <div className="flex items-center space-x-2">
          {selectedItems.length > 0 && (
            <>
              <Button variant="outline" onClick={() => setSelectedItems([])}>
                Clear ({selectedItems.length})
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="w-4 h-4 mr-2" />
                Export Selected
              </Button>
            </>
          )}
          <Button variant="outline" onClick={handleStockTransfer}>
            <ArrowRightLeft className="w-4 h-4 mr-2" />
            Transfer Stock
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
          <Button onClick={handleCreateStockItem}>
            <Plus className="w-4 h-4 mr-2" />
            Add Stock Item
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by part name, IPN, location, or batch..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <AdvancedFilter
              title="Stock Filters"
              description="Filter stock items by various criteria"
              filters={stockFilters}
              values={filterValues}
              onChange={handleFilterChange}
              onApply={handleFilterApply}
              onClear={handleFilterClear}
            />
          </div>

          {selectedItems.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''} selected
              </span>
              <Button variant="ghost" size="sm" onClick={() => setSelectedItems([])}>
                Clear selection
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Stock Items List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : (
        <>
          {/* Bulk Actions Header */}
          {stockItems.length > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={selectedItems.length === stockItems.length && stockItems.length > 0}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <span className="text-sm text-gray-600">
                  {stockItems.length} stock item{stockItems.length !== 1 ? 's' : ''} total
                </span>
              </div>
            </div>
          )}

          <div className="space-y-4">
            {stockItems.map((item) => (
              <Card key={item.pk} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <input
                        type="checkbox"
                        checked={selectedItems.includes(item.pk)}
                        onChange={() => handleSelectItem(item.pk)}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"
                      />
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <Warehouse className="w-6 h-6 text-blue-600" />
                      </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {item.part_detail.name}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(item.status_text)}`}>
                          {item.status_text}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{item.part_detail.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Part Number</p>
                          <p className="text-sm text-gray-600">{item.part_detail.IPN}</p>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-700">Location</p>
                            <p className="text-sm text-gray-600">{item.location_name}</p>
                          </div>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-700">Batch</p>
                          <p className="text-sm text-gray-600">{item.batch || 'N/A'}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Quantity</p>
                          <p className="text-lg font-semibold text-blue-600">
                            {item.quantity} {item.part_detail.units}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-700">Allocated</p>
                          <p className="text-lg font-semibold text-orange-600">
                            {item.allocated} {item.part_detail.units}
                          </p>
                        </div>
                        
                        <div>
                          <p className="text-sm font-medium text-gray-700">Available</p>
                          <p className="text-lg font-semibold text-green-600">
                            {item.available} {item.part_detail.units}
                          </p>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-4 h-4 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium text-gray-700">Last Updated</p>
                            <p className="text-sm text-gray-600">
                              {format(new Date(item.updated), 'MMM dd, yyyy')}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      {item.notes && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600">{item.notes}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex space-x-1 ml-4">
                    <Button variant="ghost" size="icon" onClick={() => handleStockAdjustment(item)}>
                      <ArrowRightLeft className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleEditStockItem(item)}>
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDeleteStockItem(item)}>
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        </>
      )}

      {stockItems.length === 0 && !isLoading && (
        <Card>
          <CardContent className="p-12 text-center">
            <Warehouse className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No stock items found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first stock item'}
            </p>
            <Button onClick={handleCreateStockItem}>
              <Plus className="w-4 h-4 mr-2" />
              Add Stock Item
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Modals */}
      {selectedStockItem && (
        <StockAdjustmentForm
          stockItem={selectedStockItem}
          isOpen={isAdjustmentOpen}
          onClose={() => {
            setIsAdjustmentOpen(false);
            setSelectedStockItem(null);
          }}
          onSave={handleSaveAdjustment}
        />
      )}

      <StockTransferForm
        stockItems={stockItems}
        isOpen={isTransferOpen}
        onClose={() => setIsTransferOpen(false)}
        onSave={handleSaveTransfer}
      />
    </div>
  );
};

export default StockPage;
