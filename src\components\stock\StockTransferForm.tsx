import React, { useState } from 'react';
import { X, Save, ArrowRight, MapPin, Package } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { StockItem } from '../../types';

interface StockTransferFormProps {
  stockItems: StockItem[];
  isOpen: boolean;
  onClose: () => void;
  onSave: (transfer: StockTransfer) => Promise<void>;
}

interface StockTransfer {
  items: {
    stockItemId: number;
    quantity: number;
  }[];
  fromLocation: number;
  toLocation: number;
  notes: string;
  reference: string;
}

const StockTransferForm: React.FC<StockTransferFormProps> = ({
  stockItems,
  isOpen,
  onClose,
  onSave,
}) => {
  const [transfer, setTransfer] = useState<StockTransfer>({
    items: [],
    fromLocation: 0,
    toLocation: 0,
    notes: '',
    reference: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const locations = [
    { id: 1, name: 'Main Warehouse - A1', description: 'Primary storage area' },
    { id: 2, name: 'Production Floor', description: 'Manufacturing area' },
    { id: 3, name: 'Quality Control', description: 'Testing and inspection' },
    { id: 4, name: 'Shipping Area', description: 'Outbound logistics' },
    { id: 5, name: 'Returns Area', description: 'Returned items processing' },
    { id: 6, name: 'Quarantine', description: 'Isolated stock' },
  ];

  const availableStockItems = stockItems.filter(item => 
    item.location === transfer.fromLocation && item.available > 0
  );

  const handleInputChange = (field: keyof StockTransfer, value: any) => {
    setTransfer(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleItemQuantityChange = (stockItemId: number, quantity: number) => {
    setTransfer(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.stockItemId === stockItemId
          ? { ...item, quantity }
          : item
      ),
    }));
  };

  const addStockItem = (stockItemId: number) => {
    const stockItem = stockItems.find(item => item.pk === stockItemId);
    if (!stockItem) return;

    setTransfer(prev => ({
      ...prev,
      items: [
        ...prev.items.filter(item => item.stockItemId !== stockItemId),
        { stockItemId, quantity: Math.min(1, stockItem.available) },
      ],
    }));
  };

  const removeStockItem = (stockItemId: number) => {
    setTransfer(prev => ({
      ...prev,
      items: prev.items.filter(item => item.stockItemId !== stockItemId),
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (transfer.fromLocation === 0) {
      newErrors.fromLocation = 'Source location is required';
    }

    if (transfer.toLocation === 0) {
      newErrors.toLocation = 'Destination location is required';
    }

    if (transfer.fromLocation === transfer.toLocation) {
      newErrors.toLocation = 'Destination must be different from source';
    }

    if (transfer.items.length === 0) {
      newErrors.items = 'At least one item must be selected for transfer';
    }

    if (!transfer.reference.trim()) {
      newErrors.reference = 'Transfer reference is required';
    }

    // Validate individual item quantities
    transfer.items.forEach((item, index) => {
      const stockItem = stockItems.find(si => si.pk === item.stockItemId);
      if (stockItem && item.quantity > stockItem.available) {
        newErrors[`item_${index}`] = `Quantity exceeds available stock (${stockItem.available})`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(transfer);
      onClose();
    } catch (error) {
      console.error('Failed to save stock transfer:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateReference = () => {
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    setTransfer(prev => ({ ...prev, reference: `ST-${date}-${random}` }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-50 rounded-lg">
                <ArrowRight className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Stock Transfer</h2>
                <p className="text-sm text-gray-500">Move stock between locations</p>
              </div>
            </div>
            <Button type="button" variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* Transfer Details */}
            <Card>
              <CardHeader>
                <CardTitle>Transfer Details</CardTitle>
                <CardDescription>Basic information about the transfer</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Transfer Reference
                    </label>
                    <div className="flex space-x-2">
                      <Input
                        value={transfer.reference}
                        onChange={(e) => handleInputChange('reference', e.target.value)}
                        placeholder="Enter reference"
                        className={errors.reference ? 'border-red-500' : ''}
                      />
                      <Button type="button" variant="outline" onClick={generateReference}>
                        Generate
                      </Button>
                    </div>
                    {errors.reference && <p className="text-red-500 text-xs mt-1">{errors.reference}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes (Optional)
                    </label>
                    <Input
                      value={transfer.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="Transfer notes"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Location Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Locations</CardTitle>
                <CardDescription>Select source and destination locations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      From Location
                    </label>
                    <select
                      value={transfer.fromLocation}
                      onChange={(e) => {
                        handleInputChange('fromLocation', parseInt(e.target.value));
                        setTransfer(prev => ({ ...prev, items: [] })); // Clear items when location changes
                      }}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                        errors.fromLocation ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value={0}>Select source location</option>
                      {locations.map((location) => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                    {errors.fromLocation && <p className="text-red-500 text-xs mt-1">{errors.fromLocation}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      To Location
                    </label>
                    <select
                      value={transfer.toLocation}
                      onChange={(e) => handleInputChange('toLocation', parseInt(e.target.value))}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                        errors.toLocation ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value={0}>Select destination location</option>
                      {locations.filter(loc => loc.id !== transfer.fromLocation).map((location) => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                    {errors.toLocation && <p className="text-red-500 text-xs mt-1">{errors.toLocation}</p>}
                  </div>
                </div>

                {transfer.fromLocation > 0 && transfer.toLocation > 0 && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg flex items-center justify-center">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <MapPin className="w-6 h-6 text-blue-600 mx-auto mb-1" />
                        <p className="text-sm font-medium">
                          {locations.find(l => l.id === transfer.fromLocation)?.name}
                        </p>
                      </div>
                      <ArrowRight className="w-6 h-6 text-blue-600" />
                      <div className="text-center">
                        <MapPin className="w-6 h-6 text-blue-600 mx-auto mb-1" />
                        <p className="text-sm font-medium">
                          {locations.find(l => l.id === transfer.toLocation)?.name}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Stock Items Selection */}
            {transfer.fromLocation > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Stock Items</CardTitle>
                  <CardDescription>Select items to transfer from the source location</CardDescription>
                </CardHeader>
                <CardContent>
                  {availableStockItems.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">
                      No stock items available at the selected location
                    </p>
                  ) : (
                    <div className="space-y-4">
                      {availableStockItems.map((stockItem) => {
                        const transferItem = transfer.items.find(item => item.stockItemId === stockItem.pk);
                        const isSelected = !!transferItem;

                        return (
                          <div key={stockItem.pk} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={isSelected}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    addStockItem(stockItem.pk);
                                  } else {
                                    removeStockItem(stockItem.pk);
                                  }
                                }}
                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              />
                              <div className="p-2 bg-blue-50 rounded-lg">
                                <Package className="w-4 h-4 text-blue-600" />
                              </div>
                              <div>
                                <p className="font-medium">{stockItem.part_detail.name}</p>
                                <p className="text-sm text-gray-500">
                                  Available: {stockItem.available} {stockItem.part_detail.units}
                                </p>
                              </div>
                            </div>

                            {isSelected && (
                              <div className="flex items-center space-x-2">
                                <label className="text-sm text-gray-600">Quantity:</label>
                                <Input
                                  type="number"
                                  value={transferItem?.quantity || 0}
                                  onChange={(e) => handleItemQuantityChange(stockItem.pk, parseInt(e.target.value) || 0)}
                                  min="1"
                                  max={stockItem.available}
                                  className="w-20"
                                />
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  )}
                  {errors.items && <p className="text-red-500 text-xs mt-2">{errors.items}</p>}
                </CardContent>
              </Card>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || transfer.items.length === 0}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Create Transfer
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StockTransferForm;
