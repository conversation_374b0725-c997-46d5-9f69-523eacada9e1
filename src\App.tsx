import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { SearchProvider } from './contexts/SearchContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/layout/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import PartsPage from './pages/parts/PartsPage';
import StockPage from './pages/stock/StockPage';
import OrdersPage from './pages/orders/OrdersPage';
import CompaniesPage from './pages/companies/CompaniesPage';
import BuildPage from './pages/build/BuildPage';
import ReportsPage from './pages/reports/ReportsPage';
import AnalyticsPage from './pages/analytics/AnalyticsPage';
import SettingsPage from './pages/settings/SettingsPage';

function App() {
  return (
    <AuthProvider>
      <SearchProvider>
        <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Dashboard />} />
            <Route path="parts" element={<PartsPage />} />
            <Route path="stock" element={<StockPage />} />
            <Route path="build" element={<BuildPage />} />
            <Route path="orders/*" element={<OrdersPage />} />
            <Route path="companies" element={<CompaniesPage />} />
            <Route path="reports" element={<ReportsPage />} />
            <Route path="analytics" element={<AnalyticsPage />} />
            <Route path="settings" element={<SettingsPage />} />
          </Route>
        </Routes>
        </Router>
      </SearchProvider>
    </AuthProvider>
  );
}

export default App;
