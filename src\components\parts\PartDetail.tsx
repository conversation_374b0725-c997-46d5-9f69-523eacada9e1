import React, { useState } from 'react';
import { X, Edit, Trash2, Package, ExternalLink, Tag, MapPin, TrendingUp, AlertTriangle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { Part } from '../../types';

interface PartDetailProps {
  part: Part;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (part: Part) => void;
  onDelete: (part: Part) => void;
}

const PartDetail: React.FC<PartDetailProps> = ({ part, isOpen, onClose, onEdit, onDelete }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'stock' | 'suppliers' | 'history'>('overview');

  if (!isOpen) return null;

  const getStockStatusColor = (part: Part) => {
    if (part.in_stock <= 0) return 'text-red-600 bg-red-50';
    if (part.in_stock <= part.minimum_stock) return 'text-yellow-600 bg-yellow-50';
    return 'text-green-600 bg-green-50';
  };

  const getStockStatusText = (part: Part) => {
    if (part.in_stock <= 0) return 'Out of Stock';
    if (part.in_stock <= part.minimum_stock) return 'Low Stock';
    return 'In Stock';
  };

  const mockStockHistory = [
    { date: '2024-01-15', action: 'Stock Added', quantity: '+50', location: 'Main Warehouse', user: 'John Doe' },
    { date: '2024-01-12', action: 'Stock Used', quantity: '-10', location: 'Production', user: 'Build Order BO-001' },
    { date: '2024-01-10', action: 'Stock Adjustment', quantity: '+5', location: 'Main Warehouse', user: 'Jane Smith' },
  ];

  const mockSuppliers = [
    { name: 'Digikey Electronics', partNumber: 'DK-ARD-UNO-R3', price: '$24.95', leadTime: '2-3 days', preferred: true },
    { name: 'Mouser Electronics', partNumber: 'MS-ARD-UNO-R3', price: '$26.50', leadTime: '1-2 days', preferred: false },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <Package className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{part.name}</h2>
              <p className="text-gray-600">{part.IPN}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`px-2 py-1 text-xs rounded-full ${getStockStatusColor(part)}`}>
                  {getStockStatusText(part)}
                </span>
                {part.active ? (
                  <span className="px-2 py-1 text-xs rounded-full text-green-600 bg-green-50">Active</span>
                ) : (
                  <span className="px-2 py-1 text-xs rounded-full text-gray-600 bg-gray-50">Inactive</span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => onEdit(part)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={() => onDelete(part)}>
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'stock', label: 'Stock' },
              { id: 'suppliers', label: 'Suppliers' },
              { id: 'history', label: 'History' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">In Stock</p>
                        <p className="text-2xl font-bold text-gray-900">{part.in_stock}</p>
                      </div>
                      <Package className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Available</p>
                        <p className="text-2xl font-bold text-green-600">{part.in_stock - 5}</p>
                      </div>
                      <TrendingUp className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Allocated</p>
                        <p className="text-2xl font-bold text-orange-600">5</p>
                      </div>
                      <AlertTriangle className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Min Stock</p>
                        <p className="text-2xl font-bold text-gray-600">{part.minimum_stock}</p>
                      </div>
                      <MapPin className="w-8 h-8 text-gray-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Part Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Part Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Description:</span>
                      <span className="font-medium">{part.description}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Category:</span>
                      <span className="font-medium">{part.category_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Units:</span>
                      <span className="font-medium">{part.units}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Revision:</span>
                      <span className="font-medium">{part.revision}</span>
                    </div>
                    {part.keywords && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Keywords:</span>
                        <div className="flex flex-wrap gap-1">
                          {part.keywords.split(',').map((keyword, index) => (
                            <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                              {keyword.trim()}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {part.link && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">External Link:</span>
                        <a
                          href={part.link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline flex items-center"
                        >
                          <ExternalLink className="w-4 h-4 mr-1" />
                          View
                        </a>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Part Properties</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {[
                      { label: 'Component', value: part.component },
                      { label: 'Assembly', value: part.assembly },
                      { label: 'Purchaseable', value: part.purchaseable },
                      { label: 'Salable', value: part.salable },
                      { label: 'Trackable', value: part.trackable },
                      { label: 'Virtual', value: part.virtual },
                    ].map((prop) => (
                      <div key={prop.label} className="flex justify-between">
                        <span className="text-gray-600">{prop.label}:</span>
                        <span className={`font-medium ${prop.value ? 'text-green-600' : 'text-gray-400'}`}>
                          {prop.value ? 'Yes' : 'No'}
                        </span>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'stock' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Stock Locations</CardTitle>
                  <CardDescription>Current stock distribution across locations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { location: 'Main Warehouse - A1', quantity: 15, batch: 'B2024-001', status: 'OK' },
                      { location: 'Production Floor', quantity: 8, batch: 'B2024-001', status: 'OK' },
                      { location: 'Quality Control', quantity: 2, batch: 'B2024-002', status: 'Testing' },
                    ].map((stock, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{stock.location}</p>
                          <p className="text-sm text-gray-500">Batch: {stock.batch}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{stock.quantity} {part.units}</p>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            stock.status === 'OK' ? 'bg-green-50 text-green-600' : 'bg-yellow-50 text-yellow-600'
                          }`}>
                            {stock.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'suppliers' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Suppliers</CardTitle>
                  <CardDescription>Supplier information and pricing</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockSuppliers.map((supplier, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <div className="flex items-center space-x-2">
                            <p className="font-medium">{supplier.name}</p>
                            {supplier.preferred && (
                              <span className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">
                                Preferred
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-500">Part #: {supplier.partNumber}</p>
                          <p className="text-sm text-gray-500">Lead Time: {supplier.leadTime}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-lg">{supplier.price}</p>
                          <Button size="sm" variant="outline">
                            Create PO
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Stock History</CardTitle>
                  <CardDescription>Recent stock movements and changes</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockStockHistory.map((entry, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border-l-4 border-blue-500 bg-blue-50">
                        <div>
                          <p className="font-medium">{entry.action}</p>
                          <p className="text-sm text-gray-600">{entry.location} • {entry.user}</p>
                          <p className="text-xs text-gray-500">{entry.date}</p>
                        </div>
                        <div className="text-right">
                          <span className={`font-medium ${
                            entry.quantity.startsWith('+') ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {entry.quantity}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PartDetail;
