import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';
import { Calendar, TrendingUp, TrendingDown, DollarSign, Package, ShoppingCart } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { Button } from '../../components/ui/Button';

const AnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d');

  // Mock data for charts
  const stockLevelData = [
    { category: 'Microcontrollers', inStock: 245, lowStock: 23, outOfStock: 5 },
    { category: 'Resistors', inStock: 1250, lowStock: 45, outOfStock: 12 },
    { category: 'Capacitors', inStock: 890, lowStock: 67, outOfStock: 8 },
    { category: 'LEDs', inStock: 567, lowStock: 34, outOfStock: 15 },
    { category: 'Sensors', inStock: 123, lowStock: 18, outOfStock: 7 },
    { category: 'Connectors', inStock: 445, lowStock: 29, outOfStock: 11 },
  ];

  const orderTrendData = [
    { month: 'Jan', purchaseOrders: 45, salesOrders: 32, buildOrders: 12 },
    { month: 'Feb', purchaseOrders: 52, salesOrders: 38, buildOrders: 15 },
    { month: 'Mar', purchaseOrders: 48, salesOrders: 42, buildOrders: 18 },
    { month: 'Apr', purchaseOrders: 61, salesOrders: 35, buildOrders: 14 },
    { month: 'May', purchaseOrders: 55, salesOrders: 48, buildOrders: 22 },
    { month: 'Jun', purchaseOrders: 67, salesOrders: 52, buildOrders: 19 },
  ];

  const supplierDistribution = [
    { name: 'Digikey', value: 35, color: '#3B82F6' },
    { name: 'Mouser', value: 28, color: '#10B981' },
    { name: 'Arrow', value: 18, color: '#F59E0B' },
    { name: 'Newark', value: 12, color: '#EF4444' },
    { name: 'Others', value: 7, color: '#8B5CF6' },
  ];

  const revenueData = [
    { month: 'Jan', revenue: 12500 },
    { month: 'Feb', revenue: 15200 },
    { month: 'Mar', revenue: 18900 },
    { month: 'Apr', revenue: 14300 },
    { month: 'May', revenue: 21800 },
    { month: 'Jun', revenue: 25600 },
  ];

  const kpiData = [
    {
      title: 'Total Revenue',
      value: '$108,300',
      change: '+12.5%',
      trend: 'up',
      icon: DollarSign,
      color: 'text-green-600',
    },
    {
      title: 'Parts in Stock',
      value: '3,520',
      change: '+5.2%',
      trend: 'up',
      icon: Package,
      color: 'text-blue-600',
    },
    {
      title: 'Active Orders',
      value: '127',
      change: '-2.1%',
      trend: 'down',
      icon: ShoppingCart,
      color: 'text-orange-600',
    },
    {
      title: 'Stock Turnover',
      value: '4.2x',
      change: '+8.7%',
      trend: 'up',
      icon: TrendingUp,
      color: 'text-purple-600',
    },
  ];

  const timeRangeOptions = [
    { value: '7d', label: '7 Days' },
    { value: '30d', label: '30 Days' },
    { value: '90d', label: '90 Days' },
    { value: '1y', label: '1 Year' },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-2">Inventory insights and performance metrics</p>
        </div>
        
        <div className="flex space-x-2">
          {timeRangeOptions.map((option) => (
            <Button
              key={option.value}
              variant={timeRange === option.value ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange(option.value as any)}
            >
              {option.label}
            </Button>
          ))}
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiData.map((kpi, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{kpi.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{kpi.value}</p>
                  <div className="flex items-center mt-1">
                    {kpi.trend === 'up' ? (
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm ${kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                      {kpi.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-50`}>
                  <kpi.icon className={`w-6 h-6 ${kpi.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Stock Levels Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Stock Levels by Category</CardTitle>
            <CardDescription>Current inventory status across part categories</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stockLevelData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="inStock" fill="#10B981" name="In Stock" />
                <Bar dataKey="lowStock" fill="#F59E0B" name="Low Stock" />
                <Bar dataKey="outOfStock" fill="#EF4444" name="Out of Stock" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Supplier Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Distribution</CardTitle>
            <CardDescription>Purchase order distribution by supplier</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={supplierDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {supplierDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Order Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Order Trends</CardTitle>
            <CardDescription>Monthly order volume by type</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={orderTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="purchaseOrders" stroke="#3B82F6" name="Purchase Orders" />
                <Line type="monotone" dataKey="salesOrders" stroke="#10B981" name="Sales Orders" />
                <Line type="monotone" dataKey="buildOrders" stroke="#F59E0B" name="Build Orders" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Revenue Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
            <CardDescription>Monthly revenue performance</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Revenue']} />
                <Bar dataKey="revenue" fill="#8B5CF6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Summary Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Parts</CardTitle>
            <CardDescription>Most frequently used parts this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: 'Arduino Uno R3', usage: 45, trend: '+12%' },
                { name: 'Resistor 10kΩ', usage: 234, trend: '+8%' },
                { name: 'LED Red 5mm', usage: 89, trend: '+15%' },
                { name: 'Capacitor 100µF', usage: 67, trend: '+5%' },
                { name: 'ESP32 DevKit', usage: 23, trend: '+22%' },
              ].map((part, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                  <div>
                    <p className="font-medium">{part.name}</p>
                    <p className="text-sm text-gray-500">{part.usage} units used</p>
                  </div>
                  <span className="text-sm text-green-600">{part.trend}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Low Stock Alerts</CardTitle>
            <CardDescription>Parts requiring immediate attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: 'Voltage Regulator 7805', current: 5, minimum: 20, status: 'critical' },
                { name: 'Crystal Oscillator 16MHz', current: 12, minimum: 25, status: 'low' },
                { name: 'Transistor 2N2222', current: 18, minimum: 30, status: 'low' },
                { name: 'Diode 1N4007', current: 3, minimum: 50, status: 'critical' },
                { name: 'IC Socket 28-pin', current: 8, minimum: 15, status: 'low' },
              ].map((part, index) => (
                <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                  <div>
                    <p className="font-medium">{part.name}</p>
                    <p className="text-sm text-gray-500">{part.current} / {part.minimum} minimum</p>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    part.status === 'critical' 
                      ? 'text-red-600 bg-red-50' 
                      : 'text-yellow-600 bg-yellow-50'
                  }`}>
                    {part.status}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsPage;
