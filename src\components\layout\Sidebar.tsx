import React from 'react';
import { NavLink } from 'react-router-dom';
import {
  Home,
  Package,
  Warehouse,
  Wrench,
  ShoppingCart,
  TrendingUp,
  Users,
  Settings,
  FileText,
  BarChart3,
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children?: NavItem[];
}

const navigationItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/',
    icon: Home,
  },
  {
    title: 'Parts',
    href: '/parts',
    icon: Package,
    children: [
      { title: 'All Parts', href: '/parts', icon: Package },
      { title: 'Categories', href: '/parts/categories', icon: Package },
      { title: 'Parameters', href: '/parts/parameters', icon: Package },
    ],
  },
  {
    title: 'Stock',
    href: '/stock',
    icon: Warehouse,
    children: [
      { title: 'Stock Items', href: '/stock', icon: Warehouse },
      { title: 'Locations', href: '/stock/locations', icon: Warehouse },
      { title: 'Stock Tracking', href: '/stock/tracking', icon: Warehouse },
    ],
  },
  {
    title: 'Build',
    href: '/build',
    icon: Wrench,
    children: [
      { title: 'Build Orders', href: '/build', icon: Wrench },
      { title: 'Bill of Materials', href: '/build/bom', icon: Wrench },
    ],
  },
  {
    title: 'Orders',
    href: '/orders',
    icon: ShoppingCart,
    children: [
      { title: 'Purchase Orders', href: '/orders/purchase', icon: ShoppingCart },
      { title: 'Sales Orders', href: '/orders/sales', icon: TrendingUp },
    ],
  },
  {
    title: 'Companies',
    href: '/companies',
    icon: Users,
  },
  {
    title: 'Reports',
    href: '/reports',
    icon: FileText,
  },
  {
    title: 'Analytics',
    href: '/analytics',
    icon: BarChart3,
  },
];

const Sidebar: React.FC = () => {
  return (
    <aside className="w-64 bg-gray-900 text-white min-h-screen">
      <nav className="p-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => (
            <NavItem key={item.href} item={item} />
          ))}
        </ul>
      </nav>
    </aside>
  );
};

interface NavItemProps {
  item: NavItem;
  level?: number;
}

const NavItem: React.FC<NavItemProps> = ({ item, level = 0 }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const hasChildren = item.children && item.children.length > 0;

  return (
    <li>
      <div>
        {hasChildren ? (
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              'w-full flex items-center px-3 py-2 text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors',
              level > 0 && 'ml-4'
            )}
          >
            <item.icon className="w-5 h-5 mr-3" />
            <span className="flex-1 text-left">{item.title}</span>
            <svg
              className={cn(
                'w-4 h-4 transition-transform',
                isOpen && 'transform rotate-90'
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        ) : (
          <NavLink
            to={item.href}
            className={({ isActive }) =>
              cn(
                'flex items-center px-3 py-2 text-sm font-medium rounded-lg hover:bg-gray-800 transition-colors',
                level > 0 && 'ml-4',
                isActive && 'bg-gray-800 text-white'
              )
            }
          >
            <item.icon className="w-5 h-5 mr-3" />
            {item.title}
          </NavLink>
        )}
      </div>

      {hasChildren && isOpen && (
        <ul className="mt-2 space-y-1">
          {item.children!.map((child) => (
            <NavItem key={child.href} item={child} level={level + 1} />
          ))}
        </ul>
      )}
    </li>
  );
};

export default Sidebar;
