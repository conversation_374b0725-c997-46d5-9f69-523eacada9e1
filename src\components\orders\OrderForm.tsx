import React, { useState, useEffect } from 'react';
import { X, Save, Plus, Trash2, ShoppingCart, TrendingUp, Calculator } from 'lucide-react';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { PurchaseOrder, SalesOrder, Company } from '../../types';

interface OrderFormProps {
  order?: PurchaseOrder | SalesOrder;
  orderType: 'purchase' | 'sales';
  isOpen: boolean;
  onClose: () => void;
  onSave: (orderData: OrderFormData) => Promise<void>;
}

interface OrderFormData {
  reference: string;
  description: string;
  company: number;
  target_date: string;
  line_items: OrderLineItem[];
  notes: string;
}

interface OrderLineItem {
  id?: number;
  part: number;
  part_name: string;
  quantity: number;
  unit_price: number;
  notes: string;
}

const OrderForm: React.FC<OrderFormProps> = ({ order, orderType, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState<OrderFormData>({
    reference: '',
    description: '',
    company: 0,
    target_date: '',
    line_items: [],
    notes: '',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mock data
  const companies: Company[] = [
    { pk: 1, name: 'Digikey Electronics', description: 'Electronic components supplier', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: false, is_supplier: true, is_manufacturer: false, currency: 'USD', active: true },
    { pk: 2, name: 'Mouser Electronics', description: 'Electronic components supplier', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: false, is_supplier: true, is_manufacturer: false, currency: 'USD', active: true },
    { pk: 3, name: 'ABC Manufacturing Corp', description: 'Industrial customer', website: '', address: '', phone: '', email: '', contact: '', link: '', image: '', is_customer: true, is_supplier: false, is_manufacturer: true, currency: 'USD', active: true },
  ];

  const parts = [
    { pk: 1, name: 'Arduino Uno R3', IPN: 'ARD-UNO-R3' },
    { pk: 2, name: 'Resistor 10kΩ', IPN: 'RES-10K-0.25W' },
    { pk: 3, name: 'LED Red 5mm', IPN: 'LED-RED-5MM' },
    { pk: 4, name: 'Capacitor 100µF', IPN: 'CAP-100UF-16V' },
  ];

  const filteredCompanies = companies.filter(company => 
    orderType === 'purchase' ? company.is_supplier : company.is_customer
  );

  useEffect(() => {
    if (order) {
      setFormData({
        reference: order.reference,
        description: order.description,
        company: orderType === 'purchase' ? (order as PurchaseOrder).supplier : (order as SalesOrder).customer,
        target_date: order.target_date.split('T')[0],
        line_items: [], // TODO: Load actual line items
        notes: '',
      });
    } else {
      // Reset form for new order
      const today = new Date();
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      
      setFormData({
        reference: generateReference(),
        description: '',
        company: 0,
        target_date: nextWeek.toISOString().split('T')[0],
        line_items: [],
        notes: '',
      });
    }
    setErrors({});
  }, [order, orderType, isOpen]);

  const generateReference = (): string => {
    const prefix = orderType === 'purchase' ? 'PO' : 'SO';
    const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}-${date}-${random}`;
  };

  const handleInputChange = (field: keyof OrderFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const addLineItem = () => {
    const newItem: OrderLineItem = {
      part: 0,
      part_name: '',
      quantity: 1,
      unit_price: 0,
      notes: '',
    };
    setFormData(prev => ({
      ...prev,
      line_items: [...prev.line_items, newItem],
    }));
  };

  const updateLineItem = (index: number, field: keyof OrderLineItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.map((item, i) => {
        if (i === index) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'part') {
            const part = parts.find(p => p.pk === value);
            updatedItem.part_name = part?.name || '';
          }
          return updatedItem;
        }
        return item;
      }),
    }));
  };

  const removeLineItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      line_items: prev.line_items.filter((_, i) => i !== index),
    }));
  };

  const calculateTotal = (): number => {
    return formData.line_items.reduce((total, item) => total + (item.quantity * item.unit_price), 0);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.reference.trim()) {
      newErrors.reference = 'Reference is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (formData.company === 0) {
      newErrors.company = `Please select a ${orderType === 'purchase' ? 'supplier' : 'customer'}`;
    }

    if (!formData.target_date) {
      newErrors.target_date = 'Target date is required';
    }

    if (formData.line_items.length === 0) {
      newErrors.line_items = 'At least one line item is required';
    }

    // Validate line items
    formData.line_items.forEach((item, index) => {
      if (item.part === 0) {
        newErrors[`line_item_${index}_part`] = 'Part is required';
      }
      if (item.quantity <= 0) {
        newErrors[`line_item_${index}_quantity`] = 'Quantity must be greater than 0';
      }
      if (item.unit_price < 0) {
        newErrors[`line_item_${index}_price`] = 'Price cannot be negative';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Failed to save order:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const total = calculateTotal();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit}>
          <div className="flex items-center justify-between p-6 border-b">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-50 rounded-lg">
                {orderType === 'purchase' ? (
                  <ShoppingCart className="w-6 h-6 text-blue-600" />
                ) : (
                  <TrendingUp className="w-6 h-6 text-blue-600" />
                )}
              </div>
              <div>
                <h2 className="text-xl font-semibold">
                  {order ? 'Edit' : 'Create'} {orderType === 'purchase' ? 'Purchase' : 'Sales'} Order
                </h2>
                <p className="text-sm text-gray-500">
                  {order ? 'Update order information' : `Create a new ${orderType} order`}
                </p>
              </div>
            </div>
            <Button type="button" variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>

          <div className="p-6 space-y-6">
            {/* Order Details */}
            <Card>
              <CardHeader>
                <CardTitle>Order Information</CardTitle>
                <CardDescription>Basic order details and company information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Reference *
                    </label>
                    <div className="flex space-x-2">
                      <Input
                        value={formData.reference}
                        onChange={(e) => handleInputChange('reference', e.target.value)}
                        placeholder="Order reference"
                        className={errors.reference ? 'border-red-500' : ''}
                      />
                      <Button type="button" variant="outline" onClick={() => handleInputChange('reference', generateReference())}>
                        Generate
                      </Button>
                    </div>
                    {errors.reference && <p className="text-red-500 text-xs mt-1">{errors.reference}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {orderType === 'purchase' ? 'Supplier' : 'Customer'} *
                    </label>
                    <select
                      value={formData.company}
                      onChange={(e) => handleInputChange('company', parseInt(e.target.value))}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                        errors.company ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value={0}>Select {orderType === 'purchase' ? 'supplier' : 'customer'}</option>
                      {filteredCompanies.map((company) => (
                        <option key={company.pk} value={company.pk}>
                          {company.name}
                        </option>
                      ))}
                    </select>
                    {errors.company && <p className="text-red-500 text-xs mt-1">{errors.company}</p>}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Order description"
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                      errors.description ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Target Date *
                    </label>
                    <Input
                      type="date"
                      value={formData.target_date}
                      onChange={(e) => handleInputChange('target_date', e.target.value)}
                      className={errors.target_date ? 'border-red-500' : ''}
                    />
                    {errors.target_date && <p className="text-red-500 text-xs mt-1">{errors.target_date}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes
                    </label>
                    <Input
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="Additional notes"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Line Items */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Line Items</CardTitle>
                    <CardDescription>Add parts and quantities to this order</CardDescription>
                  </div>
                  <Button type="button" variant="outline" onClick={addLineItem}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Item
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {formData.line_items.length === 0 ? (
                  <div className="text-center py-8">
                    <p className="text-gray-500 mb-4">No line items added yet</p>
                    <Button type="button" variant="outline" onClick={addLineItem}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add First Item
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formData.line_items.map((item, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Part *
                            </label>
                            <select
                              value={item.part}
                              onChange={(e) => updateLineItem(index, 'part', parseInt(e.target.value))}
                              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-primary focus:border-transparent ${
                                errors[`line_item_${index}_part`] ? 'border-red-500' : 'border-gray-300'
                              }`}
                            >
                              <option value={0}>Select part</option>
                              {parts.map((part) => (
                                <option key={part.pk} value={part.pk}>
                                  {part.name} ({part.IPN})
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Quantity *
                            </label>
                            <Input
                              type="number"
                              value={item.quantity}
                              onChange={(e) => updateLineItem(index, 'quantity', parseInt(e.target.value) || 0)}
                              min="1"
                              className={errors[`line_item_${index}_quantity`] ? 'border-red-500' : ''}
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Unit Price
                            </label>
                            <Input
                              type="number"
                              step="0.01"
                              value={item.unit_price}
                              onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                              min="0"
                              className={errors[`line_item_${index}_price`] ? 'border-red-500' : ''}
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Total
                            </label>
                            <div className="px-3 py-2 bg-gray-50 border rounded-md">
                              ${(item.quantity * item.unit_price).toFixed(2)}
                            </div>
                          </div>

                          <div>
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              onClick={() => removeLineItem(index)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="mt-3">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Notes
                          </label>
                          <Input
                            value={item.notes}
                            onChange={(e) => updateLineItem(index, 'notes', e.target.value)}
                            placeholder="Line item notes"
                          />
                        </div>
                      </div>
                    ))}

                    {/* Order Total */}
                    <div className="border-t pt-4">
                      <div className="flex justify-end">
                        <div className="text-right">
                          <div className="flex items-center space-x-2">
                            <Calculator className="w-5 h-5 text-gray-400" />
                            <span className="text-lg font-semibold">
                              Order Total: ${total.toFixed(2)}
                            </span>
                          </div>
                          <p className="text-sm text-gray-500">
                            {formData.line_items.length} item{formData.line_items.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {errors.line_items && <p className="text-red-500 text-xs mt-2">{errors.line_items}</p>}
              </CardContent>
            </Card>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t bg-gray-50">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {order ? 'Update Order' : 'Create Order'}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default OrderForm;
