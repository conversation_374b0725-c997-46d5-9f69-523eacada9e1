import React, { useState } from 'react';
import { X, Edit, Trash2, ShoppingCart, TrendingUp, Calendar, DollarSign, Package, CheckCircle, Clock, AlertCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import type { PurchaseOrder, SalesOrder } from '../../types';
import { format } from 'date-fns';

interface OrderDetailProps {
  order: PurchaseOrder | SalesOrder;
  orderType: 'purchase' | 'sales';
  isOpen: boolean;
  onClose: () => void;
  onEdit: (order: PurchaseOrder | SalesOrder) => void;
  onDelete: (order: PurchaseOrder | SalesOrder) => void;
  onStatusChange: (order: PurchaseOrder | SalesOrder, newStatus: number) => void;
}

const OrderDetail: React.FC<OrderDetailProps> = ({
  order,
  orderType,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onStatusChange,
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'items' | 'history' | 'attachments'>('overview');

  if (!isOpen) return null;

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50';
      case 'placed':
      case 'in progress':
        return 'text-blue-600 bg-blue-50';
      case 'complete':
      case 'shipped':
        return 'text-green-600 bg-green-50';
      case 'cancelled':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'placed':
      case 'in progress':
        return <AlertCircle className="w-4 h-4" />;
      case 'complete':
      case 'shipped':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const statusOptions = [
    { value: 10, label: 'Pending' },
    { value: 20, label: orderType === 'purchase' ? 'Placed' : 'In Progress' },
    { value: 30, label: orderType === 'purchase' ? 'Complete' : 'Shipped' },
    { value: 40, label: 'Cancelled' },
  ];

  const mockLineItems = [
    { id: 1, part_name: 'Arduino Uno R3', part_ipn: 'ARD-UNO-R3', quantity: 10, unit_price: 24.95, total: 249.50, received: orderType === 'purchase' ? 8 : undefined },
    { id: 2, part_name: 'Resistor 10kΩ', part_ipn: 'RES-10K-0.25W', quantity: 100, unit_price: 0.05, total: 5.00, received: orderType === 'purchase' ? 100 : undefined },
    { id: 3, part_name: 'LED Red 5mm', part_ipn: 'LED-RED-5MM', quantity: 50, unit_price: 0.15, total: 7.50, received: orderType === 'purchase' ? 0 : undefined },
  ];

  const mockHistory = [
    { date: '2024-01-15T10:30:00Z', action: 'Order created', user: 'John Doe', notes: 'Initial order creation' },
    { date: '2024-01-16T09:15:00Z', action: 'Order placed', user: 'Jane Smith', notes: 'Sent to supplier' },
    { date: '2024-01-18T14:20:00Z', action: 'Partial delivery', user: 'System', notes: 'Received 8/10 Arduino boards' },
  ];

  const companyDetail = orderType === 'purchase' 
    ? (order as PurchaseOrder).supplier_detail 
    : (order as SalesOrder).customer_detail;

  const orderTotal = mockLineItems.reduce((sum, item) => sum + item.total, 0);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              {orderType === 'purchase' ? (
                <ShoppingCart className="w-8 h-8 text-blue-600" />
              ) : (
                <TrendingUp className="w-8 h-8 text-blue-600" />
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{order.reference}</h2>
              <p className="text-gray-600">{companyDetail.name}</p>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`px-2 py-1 text-xs rounded-full flex items-center space-x-1 ${getStatusColor(order.status_text)}`}>
                  {getStatusIcon(order.status_text)}
                  <span>{order.status_text}</span>
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={order.status}
              onChange={(e) => onStatusChange(order, parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {statusOptions.map((status) => (
                <option key={status.value} value={status.value}>
                  {status.label}
                </option>
              ))}
            </select>
            <Button variant="outline" size="sm" onClick={() => onEdit(order)}>
              <Edit className="w-4 h-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" size="sm" onClick={() => onDelete(order)}>
              <Trash2 className="w-4 h-4 mr-2" />
              Delete
            </Button>
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'items', label: 'Line Items' },
              { id: 'history', label: 'History' },
              { id: 'attachments', label: 'Attachments' },
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Value</p>
                        <p className="text-2xl font-bold text-gray-900">${orderTotal.toFixed(2)}</p>
                      </div>
                      <DollarSign className="w-8 h-8 text-green-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Line Items</p>
                        <p className="text-2xl font-bold text-gray-900">{order.line_items}</p>
                      </div>
                      <Package className="w-8 h-8 text-blue-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Created</p>
                        <p className="text-sm font-bold text-gray-900">
                          {format(new Date(order.creation_date), 'MMM dd, yyyy')}
                        </p>
                      </div>
                      <Calendar className="w-8 h-8 text-purple-600" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Target Date</p>
                        <p className="text-sm font-bold text-gray-900">
                          {format(new Date(order.target_date), 'MMM dd, yyyy')}
                        </p>
                      </div>
                      <Clock className="w-8 h-8 text-orange-600" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Order Information */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Order Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Description:</span>
                      <span className="font-medium">{order.description}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Reference:</span>
                      <span className="font-medium">{order.reference}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status_text)}`}>
                        {order.status_text}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Currency:</span>
                      <span className="font-medium">USD</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>{orderType === 'purchase' ? 'Supplier' : 'Customer'} Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Company:</span>
                      <span className="font-medium">{companyDetail.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Contact:</span>
                      <span className="font-medium">{companyDetail.contact}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Email:</span>
                      <span className="font-medium">{companyDetail.email}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Phone:</span>
                      <span className="font-medium">{companyDetail.phone}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'items' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Line Items</CardTitle>
                  <CardDescription>Parts and quantities in this order</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Part</th>
                          <th className="text-left py-3 px-4">IPN</th>
                          <th className="text-right py-3 px-4">Quantity</th>
                          <th className="text-right py-3 px-4">Unit Price</th>
                          <th className="text-right py-3 px-4">Total</th>
                          {orderType === 'purchase' && <th className="text-right py-3 px-4">Received</th>}
                        </tr>
                      </thead>
                      <tbody>
                        {mockLineItems.map((item) => (
                          <tr key={item.id} className="border-b">
                            <td className="py-3 px-4 font-medium">{item.part_name}</td>
                            <td className="py-3 px-4 text-gray-600">{item.part_ipn}</td>
                            <td className="py-3 px-4 text-right">{item.quantity}</td>
                            <td className="py-3 px-4 text-right">${item.unit_price.toFixed(2)}</td>
                            <td className="py-3 px-4 text-right font-medium">${item.total.toFixed(2)}</td>
                            {orderType === 'purchase' && (
                              <td className="py-3 px-4 text-right">
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  item.received === item.quantity 
                                    ? 'text-green-600 bg-green-50' 
                                    : item.received === 0 
                                    ? 'text-red-600 bg-red-50' 
                                    : 'text-yellow-600 bg-yellow-50'
                                }`}>
                                  {item.received}/{item.quantity}
                                </span>
                              </td>
                            )}
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr className="border-t-2">
                          <td colSpan={orderType === 'purchase' ? 4 : 3} className="py-3 px-4 font-bold text-right">
                            Order Total:
                          </td>
                          <td className="py-3 px-4 text-right font-bold text-lg">
                            ${orderTotal.toFixed(2)}
                          </td>
                          {orderType === 'purchase' && <td></td>}
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Order History</CardTitle>
                  <CardDescription>Timeline of order events and changes</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockHistory.map((entry, index) => (
                      <div key={index} className="flex items-start space-x-4 p-4 border-l-4 border-blue-500 bg-blue-50">
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium">{entry.action}</p>
                            <span className="text-sm text-gray-500">
                              {format(new Date(entry.date), 'MMM dd, yyyy HH:mm')}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{entry.notes}</p>
                          <p className="text-xs text-gray-500 mt-1">by {entry.user}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'attachments' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Attachments</CardTitle>
                  <CardDescription>Files and documents related to this order</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Package className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No attachments found</p>
                    <Button variant="outline" className="mt-4">
                      Upload Attachment
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default OrderDetail;
